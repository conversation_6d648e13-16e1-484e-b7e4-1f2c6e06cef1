import { defineConfig } from "vitepress";
import lightbox from "vitepress-plugin-lightbox";
import { withMermaid } from "vitepress-plugin-mermaid";

declare const process: {
  env: {
    NODE_ENV: string;
  };
};

// https://vitepress.dev/reference/site-config
// export default defineConfig({
export default withMermaid({
  title: "Phoenix ZSmart Tool",
  description: "A Design Site",
  base: "/cdp/phoenix-doc/",
  markdown: {
    config: (md) => {
      md.use(lightbox, {});
    },
  },
  mermaid: {
    // mermaid主题
    theme: "default",
  },
  themeConfig: {
    outline: {
      level: [1, 4],
    },
    search: {
      provider: "local",
    },
    nav: [
      // { text: 'Home', link: '/' },
      // { text: 'Examples', link: '/markdown-examples' }
    ],

    sidebar: [
      {
        text: "集成文档",
        collapsed: false,
        items: [
          {
            text: "集成文档整体说明",
            link: "/guide/platform/platform-integration",
          },
          {
            text: "页面编排-页面流编排-集成文档",
            link: "/guide/page/page-integration",
          },
          {
            text: "逻辑编排-集成文档",
            link: "/guide/flow/flow-integration",
          },
          {
            text: "低码平台事件消息交互说明",
            link: "/guide/platform/platform-event.md",
          },
          {
            text: '低代码平台级交互说明',
            link: '/guide/platform/platform-interface.md'
          }
        ],
      },
      {
        text: '页面编排',
        collapsed: false,
        items: [
          {
            text: "全局广播事件",
            link: "/guide/page/page-global-event.md"
          }
        ]
      },
      {
        text: "页面流编排",
        collapsed: false,
        items: [
          {
            text: "简介",
            collapsed: false,
            link: "/guide/journey/journey-summary",
          },
          {
            text: "快速开始",
            collapsed: false,
            link: "/guide/journey/journey-quick-start",
          },
          {
            text: "页面配置",
            collapsed: true,
            items: [
              {
                text: "页面配置(page)",
                collapsed: false,
                link: "/guide/journey/journey-page-config-page",
              },
              {
                text: "页面配置(business)",
                collapsed: false,
                link: "/guide/journey/journey-page-config-business",
              },
            ],
          },
          {
            text: "节点",
            collapsed: true,
            items: [
              {
                text: "开始",
                collapsed: false,
                link: "/guide/journey/journey-node-start",
              },
              {
                text: "分支",
                collapsed: false,
                link: "/guide/journey/journey-node-branch",
              },
              {
                text: "子流程",
                collapsed: false,
                link: "/guide/journey/journey-node-subflow",
              },
              {
                text: "页面",
                collapsed: false,
                link: "/guide/journey/journey-node-page",
              },
            ],
          },
          {
            text: "功能操作",
            collapsed: false,
            link: "/guide/journey/journey-operation",
          },
          {
            text: "教学视频",
            collapsed: true,
            items: [
              {
                text: "page页面流",
                link: "/guide/journey/journey-video-page-flow",
              },
              {
                text: "business页面流",
                link: "/guide/journey/journey-video-business-flow",
              },
            ],
          },
        ],
      },
      {
        text: "逻辑编排",
        collapsed: false,
        items: [
          {
            text: "简介",
            link: "/guide/flow/flow-summary",
          },
          {
            text: "快速开始",
            link: "/guide/flow/flow-start",
          },
          {
            text: "配置文档",
            collapsed: true,
            items: [
              {
                text: "安全配置",
                link: "/guide/flow/flow-safe",
              },
              {
                text: "数据源配置",
                link: "/guide/flow/flow-datasource",
              },
              {
                text: "响应结构配置",
                link: "/guide/flow/flow-response",
              },
              {
                text: "事务控制",
                link: "/guide/flow/flow-transaction.md",
              },
              {
                text: "版本控制配置",
                link: "/guide/flow/flow-version.md",
              },
            ],
          },
          {
            text: "节点",
            collapsed: true,
            items: [
              {
                text: "节点概述",
                link: "/guide/flow/flow-node-summary.md",
              },
              {
                text: "开始节点",
                link: "/guide/flow/flow-startNode.md",
              },
              {
                text: "HTTP节点",
                link: "/guide/flow/flow-http.md",
              },
              {
                text: "DUBBO节点",
                link: "/guide/flow/flow-dubbo.md",
              },
              {
                text: "子流程节点",
                link: "/guide/flow/flow-subflow.md",
              },
              {
                text: "外部子流程节点",
                link: "/guide/flow/flow-externalSubFlow.md",
              },
              {
                text: "分支节点",
                link: "/guide/flow/flow-branch.md",
              },
              {
                text: "映射节点",
                link: "/guide/flow/flow-datamapping.md",
              },
              {
                text: "循环节点",
                link: "/guide/flow/flow-for.md",
              },
              {
                text: "并行节点",
                link: "/guide/flow/flow-parallel.md",
              },
              {
                text: "会话节点",
                link: "/guide/flow/flow-session.md",
              },
              {
                text: "MQ节点",
                link: "/guide/flow/flow-mq.md",
              },
              {
                text: "返回节点",
                link: "/guide/flow/flow-return.md",
              },
            ],
          },
          {
            text: "脚本节点",
            collapsed: true,
            items: [
              {
                text: "Groovy节点",
                link: "/guide/flow/flow-groovy.md",
              },
              {
                text: "Javascript节点",
                link: "/guide/flow/flow-javascript.md",
              },
              {
                text: "Java节点",
                link: "/guide/flow/flow-java.md",
              },
              {
                text: "QlExpress节点",
                link: "/guide/flow/flow-ql.md",
              },
              {
                text: "Python节点",
                link: "/guide/flow/flow-python.md",
              },
              {
                text: "Lua节点",
                link: "/guide/flow/flow-lua.md",
              },
              {
                text: "Aviator节点",
                link: "/guide/flow/flow-aviator.md",
              },
              {
                text: "Kotlin节点",
                link: "/guide/flow/flow-kotlin.md",
              },
            ],
          },
          {
            text: "自定义的节点",
            link: "/guide/flow/flow-node-customized.md",
          },
          {
            text: "运行调试控制",
            link: "/guide/flow/flow-run.md",
          },
          {
            text: "QlExpress 语法",
            link: "/guide/flow/flow-qlexpress.md",
          },
          {
            text: "FAQ 常见问题",
            collapsed: false,
            link: "/guide/flow/flow-faq.md",
          },
          {
            text: "教程视频",
            collapsed: true,
          },

        ],
      },
      {
        text: "国际化",
        items: [
          {
            text: "运行时",
            link: "/guide/i18n/runtime.md",
          },
        ],
      },
      {
        text: "教学视频",
        link: "/guide/teaching-vedios.md",
      },
    ],
  },
});
