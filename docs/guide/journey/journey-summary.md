# 简介

[[toc]]

## 什么是页面流？
&nbsp;&nbsp;&nbsp;&nbsp;页面流是指用户为完成特定任务，在前端界面所经历的有序页面跳转路径。

<div class="tip custom-block" style="padding-top: 8px">

想尝试一下？跳到[快速开始](./journey-quick-start)。

</div>

## page页面流
&nbsp;&nbsp;&nbsp;&nbsp;即普通页面流。以页面跳转为核心，强制单向线性推进，不支持返回操作。流程设计允许存在多个页面终点，用户在不同条件路径下可以抵达不同目标页。

## business页面流
&nbsp;&nbsp;&nbsp;&nbsp;即业务页面流。聚焦多步骤业务闭环，用户可自主控制进度，支持返回操作。需要严格遵循单一终点设计。

## 两者的区别

| 维度       | page页面流                         | business页面流                        |
| ---------- | ---------------------------------- | -------------------------------------- |
| 导航自由度 | 不支持返回                         | 支持上一步返回                         |
| 数据管理   | 单一跳转传参                       | 跨步骤数据缓存                         |
| 用户控制权 | 系统强制控制流程                   | 用户主导流程进度                       |
| 数据映射   | 不支持                             | 支持数据映射来适配不同页面的数据源     |
| 适用场景   | 适用跳转路径分散、数据弱耦合的场景 | 适用需要用户分步决策、数据强关联的场景 |
| 流程设计   | 多个终点                           | 一个终点                               |



