# 分支节点

[[toc]]

## 概述
&nbsp;&nbsp;&nbsp;&nbsp;分支节点可同时使用开始节点定义的全局参数及前置页面节点参数进行条件判断，并自动跳转至首个满足条件的分支路径。

## 节点配置
### 界面配置
&nbsp;&nbsp;&nbsp;&nbsp;每个分支条件对应独立连接端口，若所有配置条件均不满足，系统会自动执行预设的Else分支
![journey-node-branch-config](/images/guide/journey/journey-node-branch-config.png)

### 条件函数
&nbsp;&nbsp;&nbsp;&nbsp;通常由变量、运算符、实际值组成的判断逻辑，用于评估分支路径是否满足执行条件。

| 字段类型        | 支持函数                                                   |
| --------------- | ---------------------------------------------------------- |
| 字符串(string)  | 等于、不等于、开头为、结尾为、为空、不为空                 |
| 数字(number)    | 等于、不等于、大于、大于等于、小于、小于等于、为空、不为空 |
| 布尔值(boolean) | 等于、不等于                                               |
| 数组(array)     | 等于、不等于、大于、大于等于、小于、小于等于、为空、不为空 |
| 对象(object)    | 为空、不为空                                               |



### page和business配置区别
| 维度         | page页面流                                           | business页面流                                               |
| ------------ | ---------------------------------------------------- | ------------------------------------------------------------ |
| 参数获取途径 | 1、开始节点的全局参数<br />2、前置相邻页面的[事件参数](./journey-page-config-page#下一个页面) | 1、开始节点的全局参数<br />2、前置所有页面的[页面出参](./journey-page-config-business#页面出入参)         |
| 使用限制     | 无限制                                               | 1、分支仅用于流程简化（**节点跳过**）<br />2、仅支持配置If和Else两个分支<br />3、只能有一个分支上有节点，另一个分支需留空 |

::: warning 注意
business页面流分支仅用作节点跳过，注意使用限制
:::
