# 页面节点

[[toc]]

## 概述
&nbsp;&nbsp;&nbsp;&nbsp;即已发布的独立页面。在页面流中可以串联多个独立页面，通过编排顺序或条件分支规则，动态控制页面间的跳转路径。

## 节点配置
### 界面配置
#### page配置
&nbsp;&nbsp;&nbsp;&nbsp;独立页面可以配置多个出口事件(即[下一个页面](./journey-page-config-page#下一个页面)动作)，如提交、保存草稿、取消等，每一个事件对应一个连线端口。运行时会根据用户触发的具体事件，动态选择跳转路径。
![journey-node-page-config-page](/images/guide/journey/journey-node-page-config-page.png)

#### business配置
&nbsp;&nbsp;&nbsp;&nbsp;页面节点的入参支持手动显式映射至流程中任意前置节点的出参；若未主动配置映射关系，系统将基于同名参数规则自动完成数据转换。这里的出参仅展示使用。

![journey-node-page-config-business](/images/guide/journey/journey-node-page-config-business.png)
