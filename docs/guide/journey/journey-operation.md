# 功能操作

[[toc]]

## 概述
&nbsp;&nbsp;&nbsp;&nbsp;页面流编辑器可以基于可视化节点编排与逻辑分支配置能力，将多个独立页面动态组装为端到端可交互的业务流，实现跨页面的数据交互与场景化跳转逻辑闭环。

## 功能说明
### 测试
&nbsp;&nbsp;&nbsp;&nbsp;点击【**测试**】按钮后，系统将创建一个新的浏览器窗口，模拟运行态的页面渲染、数据交互和逻辑跳转。

![journey-operation-test](/images/guide/journey/journey-operation-test.png)

### 页面缩放
&nbsp;&nbsp;&nbsp;&nbsp;点击【**加减号**】/【**比例**】,可以自由改变画布的展示尺寸。

![journey-operation-zoom](/images/guide/journey/journey-operation-zoom.png)


### 一键全览
&nbsp;&nbsp;&nbsp;&nbsp;点击【**一键全览**】，流程节点会自动调整画布大小，像智能拼图一样铺满整个屏幕，能够一眼看清它们之间的连接关系。

![journey-operation-fitView](/images/guide/journey/journey-operation-fitView.png)

### 自动布局
&nbsp;&nbsp;&nbsp;&nbsp;点击【**自动布局**】按钮，系统会自动把零散杂乱的节点按照一定算法排成整齐的队列，让整个画面瞬间变得清爽好读。

![journey-operation-test](/images/guide/journey/journey-operation-reOrganize.png)

### 问题列表
&nbsp;&nbsp;&nbsp;&nbsp;系统实时监测流程节点配置，自动识别逻辑错误及缺失项，提示用户流程中存在的问题列表。

![journey-operation-test](/images/guide/journey/journey-operation-checklist.png)