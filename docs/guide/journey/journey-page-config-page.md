# 页面配置(page)

[[toc]]

## 概述
&nbsp; &nbsp; &nbsp; &nbsp;在配置页面流前，需要预先完成多个场景页面的搭建，通过**页面流事件**机制实现独立页面间的跳转与数据交互。

## 术语解释

| 页面流事件动作 | **定义**                                                     | 基础配置                                                     |
| -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| [下一个页面](./journey-page-config-page#下一个页面)     | 跳转至页面流中的下一个节点，支持动态参数传递                 | 1、事件名称<br />2、事件编码<br />3、页面流参数(用于目标页面的初始化或逻辑判断) |
| [更新全局数据](./journey-page-config-page#更新全局数据)   | 动态修改全局共享数据池，实现跨页面状态同步(如客户信息、系统配置等)，数据自动传递至所有关联页面 | 1、页面流参数(可用于所有页面的初始化或逻辑判断)              |


## 准备工作

- **场景页面就绪**：完成所有独立页面的功能开发与基础配置。
- **跳转关系明确**：规划页面间的跳转路径(如A→B、B→C、C→D)。
- **定义传递字段**：确认好目标页面所需的必要数据信息(例如用户ID、表单数据等)。

## 操作配置
### 页面配置

&nbsp;&nbsp;&nbsp;&nbsp;例如，设计一个简单的表单页面，配置一个“跳转”按钮。
![journey-page-config-page-form](/images/guide/journey/journey-page-config-page-form.png)

### 事件绑定

&nbsp;&nbsp;&nbsp;&nbsp;选中“跳转”按钮，点击右侧面板--事件--点击事件
![journey-page-config-page-event](/images/guide/journey/journey-page-config-page-event.png)

#### 下一个页面
&nbsp;&nbsp;&nbsp;&nbsp;配置动作【**下一个页面**】，编辑事件名称，添加需要传递的参数并绑定表单数据
![journey-page-config-page-nextPage](/images/guide/journey/journey-page-config-page-nextPage.png)

#### 更新全局数据
&nbsp;&nbsp;&nbsp;&nbsp;如实际场景需要跨页面数据共享，可再配置一个【**更新全局数据**】动作
![journey-page-config-page-updateGlobal](/images/guide/journey/journey-page-config-page-updateGlobal.png)