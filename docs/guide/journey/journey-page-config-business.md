# 页面配置(business)

[[toc]]

## 概述
&nbsp;&nbsp;&nbsp;&nbsp;在配置业务页面流前，需要预先完成多个步骤页面的搭建，通过**页面流事件**机制实现步骤页面间的返回、跳转与数据交互。

## 术语解释

| **事件动作** | **定义**                                                     | **基础配置**                                    |
| ------------ | ------------------------------------------------------------ | ----------------------------------------------- |
| [上一个步骤](./journey-page-config-business#上一个步骤)    | 返回至上一个步骤页面                                         |                                                 |
| [下一个步骤](./journey-page-config-business#下一个步骤)   | 跳转至下一个步骤页面                                         |                                                 |
| [更新步骤数据](./journey-page-config-business#更新步骤数据) | 跳转至页面流中的下一个节点，支持动态参数传递                 | 1、页面流参数(用于目标页面的初始化或逻辑判断)   |
| [更新全局数据](./journey-page-config-business#更新全局数据) | 动态修改全局共享数据池，实现跨页面状态同步(如客户信息、系统配置等)，数据自动传递至所有关联页面 | 1、页面流参数(可用于所有页面的初始化或逻辑判断) |



## 准备工作

- **场景页面就绪**：完成所有步骤页面的功能开发与基础配置。
- **跳转关系明确**：规划页面间的跳转路径(如A→B、B→C、C→D)。
- **定义页面参数**：确认好步骤页面出入参数据信息(例如用户ID、表单数据等)。

## 操作配置
### 页面配置
&nbsp;&nbsp;&nbsp;&nbsp;例如，设计一个简单的步骤页面，头部展示“步骤数”、中间是一个表单、底部展示“上一步”、“下一步”、“取消”等操作按钮。
![journey-page-config-business-form](/images/guide/journey/journey-page-config-business-form.png)

### 步骤数变量绑定

&nbsp;&nbsp;&nbsp;&nbsp;步骤数是向用户展示步骤页面所处位置(如第2步/共5步)，清晰任务当前进度。
- **当前步骤**：`$$curStep`
- **总步骤数**：`$$totalStep`
::: warning 注意
`$$curStep`和`$$totalStep`是系统自动生成的全局变量，实时反映用户在业务流程中的进度状态，其数值基于流程编排节点顺序及节点总数动态计算，无需手动维护。
:::
![journey-page-config-business-curStep](/images/guide/journey/journey-page-config-business-curStep.png)
![journey-page-config-business-totalStep](/images/guide/journey/journey-page-config-business-totalStep.png)



### 页面出入参
&nbsp;&nbsp;&nbsp;&nbsp;每个步骤页面作为独立模块化单元，通过入参接收外部传入的数据（如前置步骤的输出或全局变量），用于自身逻辑处理；同时定义出参向外暴露处理结果，作为后续步骤页面的输入依赖，实现跨步骤的数据链式传递
![journey-page-config-business-IOParam](/images/guide/journey/journey-page-config-business-IOParam.png)


### 事件绑定
#### 上一个步骤
&nbsp;&nbsp;&nbsp;&nbsp;选中“Previous”按钮，点击右侧面板--事件--点击事件，配置动作【**上一个步骤**】
![journey-page-config-business-previous](/images/guide/journey/journey-page-config-business-previous.png)

#### 下一个步骤
&nbsp;&nbsp;&nbsp;&nbsp;选中“Next”按钮，点击右侧面板--事件--点击事件，配置动作【**下一个步骤**】
![journey-page-config-business-next](/images/guide/journey/journey-page-config-business-next.png)

#### 更新步骤数据
&nbsp;&nbsp;&nbsp;&nbsp;如实际场景需要向下暴露数据，可再给“Next”按钮配置一个【**更新步骤数据**】动作。
![journey-page-config-business-updateStep](/images/guide/journey/journey-page-config-business-updateStep.png)

#### 更新全局数据
&nbsp;&nbsp;&nbsp;&nbsp;如实际场景需要跨页面数据共享，可再配置一个【**更新全局数据**】动作
![journey-page-config-business-updateGlobal](/images/guide/journey/journey-page-config-business-updateGlobal.png)


