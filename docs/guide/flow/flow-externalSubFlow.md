# 外部子流程节点

[[toc]]

## 概述
外部子流程是提供对其他已经编排好的逻辑编排的调用能力
**注意： 外部子流程节点中选择的外部子流程必须是已发布状态的， 如果未发布， 那么在外部子流程的选择弹窗中将搜索不到该流程**
![flow-externalSubflow-all](/images/guide/flow/flow-externalSubflow-all.png)

## 配置方式
* Transaction Configuration: 外部子流程子流程支持事务控制能力
* Subflow Select: 可点击铅笔图标进行外部子流程的选择， 外部子流程选择弹窗支持点击preview按钮进行外部子流程的选择。注意：“刷新按钮”指的是: 如果使用的外部的子流程有更改， 当前的流程又需要更新外部子流程的出入参， 就可以点击这个按钮， 点击后当前界面中的关于外部子流程节点的出入参会自动刷新。
* Request Information、Response Information: 表示使用的外部子流程的出、入参。如果外部子流程有多个不同状态码的return节点， 界面显示上我们默认显示将200的返回码作为该外部子流程的返回。实际结果中， $.ExternalSubflow 代码外部子流程的实际返回结果， 所以你仍旧可以取到外部子流程报错时候的返回结果。

![flow-externalSubFlow-config](/images/guide/flow/flow-externalSubFlow-config.png)



## 重要注意事项
**举例： 当一个逻辑编排的A流程已经被其他流程B作为外部子流程“引用之后”， 如果再去修改A, 点击保存的时候， 会有警告性的提示： 在该提示中， 会列出当前流程被哪些流程B引用了，并告知你要去检查一下那些B流程。 比如： A流程增加了一个必要参数，如果你什么都不做， 那些B流程肯定就跑不通了，那么你就要去B里面使用A的地方去刷新**
![flow-exteranlsubflow-important](/images/guide/flow/flow-exteranlsubflow-important.png)
