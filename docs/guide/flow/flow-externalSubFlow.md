# 外部子流程节点

[[toc]]

## 概述

外部子流程节点提供调用其他已编排逻辑流程的能力，实现流程复用和模块化设计。

**重要注意事项**：
- 只能选择**已发布**的外部子流程
- 未发布的子流程不会出现在选择弹窗的搜索结果中

![flow-externalSubflow-all](/images/guide/flow/flow-externalSubflow-all.png)

## 配置方式

### 1. 事务配置 (Transaction Configuration)
- 支持对外部子流程的事务控制
- 详细配置参考[事务配置文档](./flow-transaction.md)

### 2. 子流程选择 (Subflow Select)
- 点击铅笔图标打开选择弹窗
- 支持通过 **Preview** 按钮预览子流程
- **刷新按钮**功能：
  - 当外部子流程有变更时使用
  - 自动更新当前流程中该节点的出入参定义

### 3. 请求/响应信息 (Request/Response Information)
- 显示外部子流程的出入参定义
- 默认显示 200 状态码的返回结构
- 实际返回可通过 `$.ExternalSubflow` 获取（包含错误情况下的返回结果）

![flow-externalSubFlow-config](/images/guide/flow/flow-externalSubFlow-config.png)

## 重要注意事项

### 修改被引用的子流程
当逻辑编排流程A被其他流程B引用后：

1. 修改A流程并保存时，系统会显示警告提示
2. 提示内容包含：
   - 当前流程被哪些流程引用（如流程B）
   - 需要检查这些引用流程的兼容性

**示例场景**：
- 如果为流程A新增了必填参数
- 必须到所有引用A的流程B中：
  1. 找到对应的外部子流程节点
  2. 点击"刷新"按钮更新参数定义
  3. 确保流程兼容性

![flow-exteranlsubflow-important](/images/guide/flow/flow-exteranlsubflow-important.png)
