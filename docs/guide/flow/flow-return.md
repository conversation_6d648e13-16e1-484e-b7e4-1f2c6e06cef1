# 返回节点

[[toc]]

## 概述

返回节点（Return 节点）是逻辑编排中的终止节点，用于定义流程的最终输出结果。当流程作为接口被调用时，该节点决定返回的状态码和响应内容。

**核心功能**：
- 定义 HTTP 状态码
- 指定流程返回值
- 作为流程的终止端点

![flow-return-all](/images/guide/flow/flow-return-all.png)

## 配置方式

### 1. HTTP 状态码配置
- 定义流程返回的 HTTP 状态码（如：200、400、500等）
- 支持标准 HTTP 状态码

### 2. HTTP 返回结果
- 定义流程的响应内容
- 支持结构化数据返回（JSON/XML）

![flow-return-config](/images/guide/flow/flow-return-config.png)

## 配置注意事项

### 返回结构定义规则
1. **不可直接自定义**：不能在 Return 节点中直接定义返回结构
2. **自动关联显示**：
   - 选择状态码后，系统自动显示预定义的响应结构
   - 显示内容基于流程中配置的状态码与响应结构的映射关系

### 响应结构配置
- 详细配置方法请参考：[响应结构配置文档](./flow-response.md)
- 建议提前规划好所有可能的返回状态和数据结构

![flow-return-important](/images/guide/flow/flow-return-important.png)

## 最佳实践

1. **状态码规划**：
   - 遵循 RESTful 规范使用 HTTP 状态码
   - 业务错误建议使用 4xx 系列状态码

2. **返回结构设计**：
   - 保持数据结构一致性
   - 错误响应包含错误代码和描述信息

3. **多状态码场景**：
   - 为每个业务场景定义明确的返回状态
   - 在流程中配置对应的返回节点分支
