# 返回节点

[[toc]]

## 概述
返回节点(Return节点)用于定义逻辑编排的返回状态码(流程作为接口被调用时)、返回值。
![flow-return-all](/images/guide/flow/flow-return-all.png)

## 配置方式
* Http Status配置：定义流程的http状态码
* HTTP Result: 定义流程的返回结果
![flow-return-config](/images/guide/flow/flow-return-config.png)

## 配置注意事项
**return节点是不能直接在节点中自定义返回结构的， 当你选择状态码之后， 会根据当前流程中的状态码和返回结构的关系自动显示对应状态码的响应结构， 而流程中状态码和响应结构的设置可参考： [配置文档-响应结构配置](./flow-response.md)这一章节， 有较详细的说明**
![flow-return-important](/images/guide/flow/flow-return-important.png)
