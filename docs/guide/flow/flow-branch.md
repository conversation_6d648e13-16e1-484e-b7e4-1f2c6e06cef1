# 分支节点

[[toc]]

## 概述
分支节点是一个非常重要的控制结构，它允许流程根据特定条件走向不同的路径。区分于其他的"单进单出的节点"， 分支节点有着"单进多出"的特点。

**注意：在配置变量规则的时候, 采用的语法QlExpress, 可参考[QlExpreess语法](./flow-qlexpress)**

## 配置方式
* 通过新增按钮增加判断分支
* 通过输入框填写判断分支的条件,分支条件的语法QlExpress, 可参考[QlExpreess语法](./flow-qlexpress)
![http-branch-config](/images/guide/flow/http-branch-config.png)

## 执行效果说明
测试运行的结果说明了判断分支的最终走向：如下图所示：
![flow-branch-execute](/images/guide/flow/flow-branch-execute.png)
