# FAQ 常见问题

[[toc]]

# 概述
本章节收集了一些逻辑编排在使用过程中常见的问题和解决方案, 涵盖基础配置、流程设计、调试排错等场景,帮助您快速定位和解决问题.

## 配置、流程类问题
#### 新建的逻辑编排A在某个逻辑编排B的外部子流程节点中选不到？
::: warning 如何解决?
新建的外部子流程一定要发布, 外部子流程选择面板只能选择处于发布状态的的外部子流程
:::

<!-- ::: warning 如何解决?
::: -->

#### 下载功能中找不到自己新建的逻辑编排?
::: warning 如何解决?
下载面板中只能选择处于发布状态的的外部子流程, 请先发布后再勾选下载.
:::

#### 返回节点无法新增、编辑返回的数据结构?
::: warning 如何解决?
返回节点中只能给结构配置具体的映射规则,而其返回结构是通过选择的状态码关联"在响应结构配置中配置的数据".详细操作可以阅读 [响应结构配置](../flow/flow-response)
:::

#### 在Start节点中, 如果Query、Header参数的某个字段如果定义成了数组类型? 在run debug的时候, debug弹窗的该字段的输入框该如何填写？
::: warning 如何解决?
比如定义成了一个字符串数组, 直接输入以逗号分隔的字符串即可, eg. name,age,gender
比如定义成了一个数字数组, 直接输入以逗号分隔的数字即可, eg. 1,2,3,4,5

其他类型的类似处理即可.
:::

#### 如何统一项目的返回结构？
::: warning 如何解决?
通过全局响应结构配置配置全局结构, 如果某些接口和全局结构有差异, 在流程图的顶部响应结构配置中选择对应的状态码, 再做二次修改. 详细操作可以阅读 [响应结构配置](../flow/flow-response)
:::

#### 流程图上有一个200状态的节点了,当我去修改200对应的返回结构时, 流程图上已经配置好的映射会丢失吗?
::: warning 如何解决?
当我去修改200对应的返回结构时,第一步流程图上的结构会被刷新, 第二步流程图上修改以前的和修改以后的同路径结构下字段的映射不会丢失, 举个例子： 修改前和修改后的顶层都有一个name字段,修改后name字段的映射刷新不会丢失.

**注意: 同路径指的是 同字段名称**
:::

#### 如何在流程图上取循环节点的结果值?
::: warning 如何解决?
循环节点的最终结果是一个数组,是循环节点中的最后一个节点的返回值组成的数组,前提条件: 循环节点内的最后一个节点如果是一个"有值的节点(比如映射节点)"
:::

#### 如何在流程图的循环节点中取到当前的循环项?
::: warning 如何解决?
* 如果循环的是次数,那就无当前项这一说
* 如果循环的是数组,eg. 循环的是$.testArr.size(). 那么获取当前项可以使用$.testArr[$.LoopStart.index] ($.LoopStart为循环开始节点的返回值,是一个对象, 里面有一个名为index的key值)
:::

#### 循环节点、子流程、并行节点支持整体复制功能吗？
::: warning 如何解决?
**暂时不支持:** 因为涉及到内部节点的逻辑、节点名称需要变更等等原因, 为防止混乱暂时不支持这几个节点的整体复制功能.
:::

#### 并行节点里面支持嵌套循环节点吗?
::: warning 如何解决?
**暂时不支持:** 可以先将循环节点的逻辑抽取到外部子流程中去, 再在并行节点中调用外部子流程.
:::


#### 脚本节点中设置了返回值 后续的节点为什么取不到?
::: warning 如何解决?
在putContext向上下文中设置节点数据的时候,putContext的第一个参数必须是当前节点的节点名称, 这样才能保证设置到对应的节点上、


```python
// Python节点 设置返回值如下
phoContext.putContext("Python",{
  "pythonName": "pythonName",
  "pythonAge": 122
})

// Python2节点 设置返回值如下
phoContext.putContext("Python2",{
  "pythonName": "pythonName",
  "pythonAge": 122
})
```
:::

#### Python节点中使用中文字符串、中文字符串拼接会导致报错?
::: warning 如何解决?
python节点中涉及到中文字符串,建议先使用Unicode变量声明方式定义一下这个中文字符串, 定义之后再进行拼接等等操作.

下面是一个示例
```python
testString = u"我是一个中文字符串"
```
:::

#### 外部子流程的出入参变了,如何知道哪些流程用了这个外部子流程以及如何在其他主流程里面更新这个外部子流程的出入参?
::: warning 如何解决?
* **如何知道哪些流程用了这个外部子流程:** 外部子流程在保存的时候,会提示哪些流程引用了当前这个外部子流程
* **如何在其他流程里面更新这个外部子流程的出入参:** 在选择外部子流程的配置面板中, 选择外部子流程的表单项中有一个刷新图标, 可以点击刷新按钮, 刷新完界面会显示最新的出入参结构,以及会保留修改前后的同路径的字段的映射规则
:::

#### 集成了逻辑编排之后, 逻辑编排中某些节点不显示?
::: warning 如何解决?
* 可能性一: 自定义节点: 检查后端的代码,是否注册了插件?

* 可能性二: 逻辑编排内置节点: 是否增加了配置项(phoenix.components.exclude)将某些节点排掉了

**全量的节点配置如下:** dubboCmp, mqCmp, workFlowCmp, sessionCertificationCmp, kotlinCmp, javaCmp, pythonCmp, qlExpressCmp, aviatorCmp, luaCmp, groovyCmp, javascriptCmp, subflowCmp, forCmp, branchCmp, returnCmp, restCmp, convertCmp, exterSubFlowCmp, parallelCmp
:::

#### 集成了逻辑编排之后, 如何屏蔽某些节点的显示?
::: warning 如何解决?
可以通过增加配置项(phoenix.components.exclude)将不想显示的节点排除掉,eg.phoenix.components.exclude=dubboCmp,mqCmp => 界面不显示DUBBO节点和MQ节点

**全量的节点配置如下:**  dubboCmp, mqCmp, workFlowCmp, sessionCertificationCmp, kotlinCmp, javaCmp, pythonCmp, qlExpressCmp, aviatorCmp, luaCmp, groovyCmp, javascriptCmp, subflowCmp, forCmp, branchCmp, returnCmp, restCmp, convertCmp, exterSubFlowCmp, parallelCmp
:::

#### 多分支能否合并连接到返回节点?
::: warning 如何解决?
**不可以:** 出于方便定位、区分不同分支对应不同结果更加明显、同一return不方便返回多个分支中的数据等这些考虑, 目前还不支持合并的操作.

**那如果多个返回节点的return结构一致怎么办? 重复操作过高? 可以看看下面针对在编排逻辑的时候的一些优化建议:**

* 如果判断分支是为了做逻辑区分比较大的两个逻辑, 那目前只能是这样连接多个返回节点, 但是可以通过复制节点(对着节点右击、悬浮节点点击节点的更多操作)的功能减少一些重复工作量.
* 如果分支节点后面的逻辑比较类似, 比如判断某个变量调同一个接口传不同参数, 那么可以不用分支节点, 直接在datamapping等映射里面做判断
:::

## 调试类问题

#### 为什么我本地run的时候好的,但测试环境会报错parse el fail in this chain[6600]; run QlExpress Exception at line 1 ?
::: warning 如何解决?
* 可能性一: 可以通过日志看一下是哪个节点未引入.
:::

#### 我编辑的逻辑编排点击 run打开运行测试弹窗,运行按钮时灰色的,无法点击?
::: warning 如何解决?
**逻辑编排的节点缺失必配的配置时,是无法测试运行的:** 请先检查流程图下方工具栏最右侧的Check List图标将缺失的必要配置项配置完成再重试即可.
:::

#### 我在编辑态的运行结果是正确的,但是在runtime 接口调用的时候, 返回参数和编辑态不一致, 可能是什么情况?
::: warning 如何解决?
* 可能性一: 最新的编辑态版本是否先发布并通过下载、推送、本地提交等方式提交到了runtime对应的位置,
否则可能会出现编辑态和runtime运行的逻辑编排不一致的情况(**特别注意: 推送下载功能都是针对发布状态的逻辑编排流, 所以如果编辑的flow不是发布状态的,一定要先发布**)
* 可能性二: 请检查编辑态和runtime连接的环境是否是同一环境,如果不是同一环境, 那么可能是环境数据的问题
:::
