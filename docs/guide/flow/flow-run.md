# 运行调试控制

[[toc]]

## 概述
专为复杂流程验证设计的可视化调试环境，提供节点运行的实时监控和完整的运行结果查看。具备以下核心亮点：
* 执行流图谱
* 数据追踪面板
* 性能分析：统计每个节点耗时，识别瓶颈环节
* 错误溯源：显示每个节点的运行错误情况

## 配置方式
### 调试启动位置
调试的位置如下图所示，点击按钮可打开调试界面

**注意：在开始启动调试前, 最好将Check List中的告警错误清除掉，避免因为一些必要的配置项没有配置导致逻辑编排的运行失败**
![flow-run-position](/images/guide/flow/flow-run-position.png)

### 调试操作界面
#### INPUT面板
输入参数，这里的可输入会根据使用者在start节点中定义的参数结构渲染

**注意: 如果是数组形式的参数， 输入的时候如图所示， 按照逗号分隔即可**
![flow-run-input](/images/guide/flow/flow-run-input.png)

#### DETAIL看板
这个面板会返回一些运行的最终结果、消耗时间、开始结束时间等信息
![flow-run-detail](/images/guide/flow/flow-run-detail.jpg)

#### TRACING面板
跟踪面板会详细列出所有的运行的节点的出参、入参， 同时如果某个节点报错，对应节点的输出面板会显示对应的报错
![flow-run-tracing](/images/guide/flow/flow-run-tracing.jpg)

### 界面调试结果查看
#### 界面运行
流程在运行的过程中会显示节点的正在运行效果动画， 执行成功节点会显示对应的成功状态， 失败则会有失败的状态
![flow-run-execute](/images/guide/flow/flow-run-execute.png)

#### 运行结果查看
在TRACING面板中可查看详细的节点的输入输出结果， 如果有报错， 在对应节点的输出面中可以查看具体的错误详情
![flow-run-error](/images/guide/flow/flow-run-error.jpg)
