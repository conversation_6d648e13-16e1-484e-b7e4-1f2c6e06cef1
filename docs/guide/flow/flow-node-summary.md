# 节点概述

[[toc]]

## 概述

### 语法规范说明
在配置变量规则时需注意语法差异：

| 节点类型       | 使用语法        | 参考文档                          |
|----------------|---------------|----------------------------------|
| 脚本节点       | 各语言原生语法   | 根据语言类型参考对应文档          |
| 其他所有节点   | QlExpress语法  | [QlExpress语法指南](./flow-qlexpress) |

> **重要提示**：
> - 除脚本节点外，所有配置规则、变量表达式均需遵循QlExpress语法规范
> - 混合使用不同语法会导致解析失败

### 语法应用示例
```java
// QlExpress语法示例（条件判断）
if (order.total > 1000) {
    return "VIP";
} else {
    return "Standard";
}
```