# 安全配置

[[toc]]

## 概述

逻辑编排目前支持以下几种鉴权方式：

* No Auth：不采用任何鉴权方式
* Basic Auth：客户端发送用户名和密码的 Base64 编码（未加密）到服务端，格式为 `Authorization: Basic base64(username:password)`
* Bearer Token：客户端在请求头中携带令牌（`Authorization: Bearer <token>`），服务端通过验证令牌的合法性来授权访问
* Api Key：客户端使用服务端分配的固定密钥（如 `API-Key: xxxxxx`）验证身份

## 全局安全配置和局部安全配置

逻辑编排支持配置全局的鉴权方式， 也支持针对单个的逻辑流配置鉴权方式，应用关系为：

1. 如果单个逻辑流配置了鉴权方式， 那么则以自身配置的为
2. 如果单个逻辑流未配置， 但是全局配置了鉴权方式， 则默认会继承全局的鉴权方式

全局鉴权配置和单个逻辑流的鉴权配置的位置如下所示：
![flow-auth-position](/images/guide/flow/flow-auth-position.png)

## 配置方式
### No Auth
通过界面配置 直接选中No Auth选项即可
![flow-no-auth](/images/guide/flow/flow-no-auth.png)

### Basic Auth
填写参数、用户名即可
![flow-basic-auth](/images/guide/flow/flow-basic-auth.png)

### Bearer Token
Bearer Token鉴权方式会默认生成一个符合规范的令牌
![flow-bearer-token](/images/guide/flow/flow-bearer-token.png)

### Api Key
填写鉴权的key、value参数， 注意Add To, 有两个可选项:
1. Header：通过http的请求头进行传递(**推荐**)
2. Query Params：通过接口的地址参数进行拼接传递
![flow-api-key](/images/guide/flow/flow-api-key.png)

