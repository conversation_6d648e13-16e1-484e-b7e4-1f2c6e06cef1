# 安全配置

[[toc]]

## 概述

逻辑编排目前支持以下几种鉴权方式：

* No Auth：不采用任何鉴权方式
* Basic Auth：客户端发送用户名和密码的 Base64 编码（未加密）到服务端，格式为 `Authorization: Basic base64(username:password)`
* Bearer Token：客户端在请求头中携带令牌（`Authorization: Bearer <token>`），服务端通过验证令牌的合法性来授权访问
* Api Key：客户端使用服务端分配的固定密钥（如 `API-Key: xxxxxx`）验证身份

## 全局安全配置和局部安全配置

逻辑编排支持配置全局的鉴权方式， 也支持针对单个的逻辑流配置鉴权方式，应用关系为：

1. 如果单个逻辑流配置了鉴权方式, 那么则以自身配置的为准
2. 如果单个逻辑流未配置， 但是全局配置了鉴权方式， 则默认会继承使用全局的鉴权方式

全局鉴权配置和单个逻辑流的鉴权配置的位置如下所示：
![flow-auth-position](/images/guide/flow/flow-auth-position.png)

## 配置方式
### No Auth
通过界面配置 直接选中No Auth选项即可
![flow-no-auth](/images/guide/flow/flow-no-auth.png)

### Basic Auth
填写参数、用户名即可
![flow-basic-auth](/images/guide/flow/flow-basic-auth.png)

### Bearer Token
Bearer Token鉴权方式会默认生成一个符合规范的令牌, 注意这个token值不可手动修改, 只能通过表单项后面的刷新按钮来重新生成
![flow-bearer-token](/images/guide/flow/flow-bearer-token.png)

### Api Key
填写鉴权的key、value参数， 注意Add To, 有两个可选项:
1. Header：通过http的请求头进行传递(**推荐**)
2. Query Params：通过接口的地址参数进行拼接传递
![flow-api-key](/images/guide/flow/flow-api-key.png)

## 接口调用如何传递鉴权
当逻辑编排开启了鉴权方式之后, 作为接口调用的时候按照如下方式进行调用.
* **Basic Auth:** 传递一个key名为Authorization的header字段, Authorization: Basic base64(username:password), 注意这里的base64(username:password)不是一个字符串, 而是将username:password进行base64编码之后的字符串
* **Bearer Token:** 传递一个key名为Authorization的header字段, Authorization: Bearer xxx   这个xxx就是上面配置的token值, 注意Bearer和token值之间有一个空格
* **Api Key:** Api Key有两个参数, key和value, 传递方式有两种(具体以哪种方式传递根据界面上配置的鉴权方式为准):

    1、方式一 传递到header: 传递一个key名为key的header字段, value为上面配置的value值

    2、方式二 传递到query params: 通过url后面接query参数来传递. eg. http://10.10.10.10/api/data?key=value


