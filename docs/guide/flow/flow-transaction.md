# 事务控制

[[toc]]

## 概述
逻辑编排支持事务能力，包括：
- **流程本身的事务能力**
- **部分节点的事务能力**（当前支持节点类型）：
  - 子流程节点
  - 外部子流程节点
  - 循环节点

事务控制能力支持灵活配置：
- 可动态开启/关闭
- 支持选择事务类型：`集成` 或 `新起`


特性：
- 支持覆盖全局事务设置
- 可单独配置事务传播行为

## 配置方式
### 逻辑编排事务能力
![flow-transaction-flow](/images/guide/flow/flow-transaction-flow.png)

### 节点事务能力
#### 子流程节点
![flow-transaction-subflow](/images/guide/flow/flow-transaction-subflow.png)

#### 外部子流程节点
![flow-transaction-extrenal-subflow](/images/guide/flow/flow-transaction-extrenal-subflow.png)

#### 循环节点
![flow-transaction-for](/images/guide/flow/flow-transaction-for.png)

