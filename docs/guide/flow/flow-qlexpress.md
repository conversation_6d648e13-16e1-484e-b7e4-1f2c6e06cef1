# QLExpress语法

[[toc]]

## 概述

QLExpress由阿里的电商业务规则、表达式（布尔组合）、特殊数学公式计算（高精度）、语法分析、脚本二次定制等强需求而设计的一门动态脚本引擎解析工具。 在阿里集团有很强的影响力，同时为了自身不断优化、发扬开源贡献精神，于2012年开源。

QLExpress脚本引擎被广泛应用在阿里的电商业务场景，具有以下的一些特性:

*   线程安全，引擎运算过程中的产生的临时变量都是threadlocal类型。
    
*   高效执行，比较耗时的脚本编译过程可以缓存在本地机器，运行时的临时变量创建采用了缓冲池的技术，和groovy性能相当。
    
*   弱类型脚本语言，和groovy，javascript语法类似，虽然比强类型脚本语言要慢一些，但是使业务的灵活度大大增强。
    
*   安全控制,可以通过设置相关运行参数，预防死循环、高危系统api调用等情况。
    
*   代码精简，依赖最小，250k的jar包适合所有java的运行环境，在android系统的低端pos机也得到广泛运用。
    

*   QLExpress的Github官网地址如下，上面有很多语法介绍材料：
    

[https://github.com/alibaba/QLExpress: https://github.com/alibaba/QLExpress](https://github.com/alibaba/QLExpress)

*   当前我们使用的QLExpress版本为：**3.3.2**
    
*   我们提供了一个简单的QLExpress调试工程，**参考第4章节。**
    

## QLExpress语法介绍

### 操作符和java对象操作

#### 普通java语法

```javascript
//支持 +,-,*,/,<,>,<=,>=,==,!=,<>【等同于!=】,%,mod【取模等同于%】,++,--,
//in【类似sql】,like【sql语法】,&&,||,!,等操作符
//支持for，break、continue、if then else 等标准的程序控制逻辑
n = 10;
sum = 0;
for(i = 0; i < n; i++) {
   sum = sum + i;
}
return sum;

//逻辑三元操作
a = 1;
b = 2;
maxnum = a > b ? a : b;
```

### 和java语法相比，要避免的一些ql写法错误

*   不支持try{}catch{}
    
*   注释目前只支持 //，不支持单行注释 //
    
*   不支持java8的lambda表达式
    
*   不支持for循环集合操作for (Item item : list)
    
*   弱类型语言，请不要定义类型声明,更不要用Template（Map<String, List>之类的）
    
*   array的声明不一样
    
*   **min,max,round,print,println,like,in** 都是系统默认函数的关键字，请不要作为变量名
    

```java
//java语法：使用泛型来提醒开发者检查类型
keys = new ArrayList<String>();
deviceName2Value = new HashMap<String, String>(7);
String[] deviceNames = {"ng", "si", "umid", "ut", "mac", "imsi", "imei"};
int[] mins = {5, 30};

//ql写法：
keys = new ArrayList();
deviceName2Value = new HashMap();
deviceNames = ["ng", "si", "umid", "ut", "mac", "imsi", "imei"];
mins = [5, 30];

//java语法：对象类型声明
FocFulfillDecisionReqDTO reqDTO = param.getReqDTO();
//ql写法：
reqDTO = param.getReqDTO();

//java语法：数组遍历
for(Item item : list) {
}
//ql写法：
for(i = 0; i < list.size(); i++){
    item = list.get(i);
}

//java语法：map遍历
for(String key : map.keySet()) {
    System.out.println(map.get(key));
}
//ql写法：
keySet = map.keySet();
objArr = keySet.toArray();
for (i = 0; i < objArr.length; i++) {
    key = objArr[i];
    System.out.println(map.get(key));
}
```

#### java的对象操作

```java
import com.ql.util.express.test.OrderQuery;
//系统自动会import java.lang.*,import java.util.*;

query = new OrderQuery();           // 创建class实例，自动补全类路径
query.setCreateDate(new Date());    // 设置属性
query.buyer = "张三";                // 调用属性，默认会转化为setBuyer("张三")
result = bizOrderDAO.query(query);  // 调用bean对象的方法
System.out.println(result.getId()); // 调用静态方法
```

### 脚本中定义function

```javascript
function add(int a, int b){
    return a + b;
};

function sub(int a, int b){
    return a - b;
};

a = 10;
return add(a, 4) + sub(a, 9);
```

**注意以下脚本int和没有int的区别**

```java
String express = "int 平均分 = (语文 + 数学 + 英语 + 综合考试.科目2) / 4.0; return 平均分";
ExpressRunner runner = new ExpressRunner(true, true);
String[] names = runner.getOutVarNames(express);
for(String s:names){
    System.out.println("var : " + s);
}

//输出结果：
var : 数学
var : 综合考试
var : 英语
var : 语文
```

### 关于集合的快捷写法

```java
@Test
public void testSet() throws Exception {
    ExpressRunner runner = new ExpressRunner(false, false);
    DefaultContext<String, Object> context = new DefaultContext<String, Object>();
    String express = "abc = NewMap(1:1, 2:2); return abc.get(1) + abc.get(2);";
    Object r = runner.execute(express, context, null, false, false);
    System.out.println(r);
    express = "abc = NewList(1, 2, 3); return abc.get(1) + abc.get(2)";
    r = runner.execute(express, context, null, false, false);
    System.out.println(r);
    express = "abc = [1, 2, 3]; return abc[1] + abc[2];";
    r = runner.execute(express, context, null, false, false);
    System.out.println(r);
}
```

### 集合的遍历

其实类似java的语法，只是ql不支持for(obj:list){}的语法，只能通过下标访问。

```java
//遍历map
map = new HashMap();
map.put("a", "a_value");
map.put("b", "b_value");
keySet = map.keySet();
objArr = keySet.toArray();
for (i = 0; i < objArr.length; i++) {
    key = objArr[i];
    System.out.println(map.get(key));
}
```

### 获取元素

*   获取传入参数的表达式：$.Start.body.subsId
    

:::
$是所有数据的根节点，第2个路径名称代表的是编排流程中的一个节点，例如Start代表的是开始节点。第3个路径名称是Start节点的body入参。
:::

*   获取数组元素的表达式：$.querySubsByAccNbr.response.value\[0\].subsId
    

:::
表示获取querySubsByAccNbr服务返回参数value数组第1条记录中的subsId字段

**PS：value和\[\]之间不要带“.”，界面的提示有问题。**
:::

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/1wvqr7gRB69vOako/img/bd98acbe-c7d0-4ffc-a1b9-6b69c918ee86.png)

*   获取带有特殊字符的表达式：$.querySubsByAccNbr.response\["@odata.hasValue"\]
    

:::
@odata.hasValue是OData接口返回的一个参数，因为带有英文“.”，所以正常的路径表达式不能使用，所以可以改用这种方式拉获取数据；
:::

### 数据比较（返回true或false）

*   元素判空
    

:::
$.response.errorDesc == null || "".equals($.response.errorDesc)
:::

*   元素值比较
    

:::
"40700775".equals($.response.errorCode)
:::

*   错误码是否存在于预定的错误码列表中。（PS，后续的错误码常量可以写很多个）
    

:::
OdhContains($.response.errorCode, '40502262', '40502938')
:::

*   按顺序返回多个值中不为空的那个（入参必须大于等于2个）
    

:::
OdhReturnNotEmpty($.response.custName, $.response.custNbr, $.response.custCode)
:::

*   判断列表字段是否为空
    

:::
$.response.attrs != null && $.response.attrs.isEmpty()
:::

## 执行脚本样例

[https://git-nj.iwhalecloud.com/cdpdevmanager/odh-ql-test.git](https://git-nj.iwhalecloud.com/cdpdevmanager/odh-ql-test.git)

以上是一个QLExpress执行脚本的测试验证工程，可以在这里编写报文并模拟验证QL的脚本。

### 将传入的body参数设置到query中

```plsql
$.request.path.productOfferingId = $.request.body.productOfferingCode;
```

### 将相应的数据移动到productOfferingList节点下

```java
if ($.response != null && $.response.size() > 0) {
    respData = new ArrayList();
    for (i = 0; i < $.response.size(); i++) {  
        respData.add($.response.get(i).clone());  
    }
    $.response.clear();
    $.response = new HashMap();
    $.response.put('productOfferingList', respData);
} else {
    $.response = new ArrayList();
}
```

### for循环遍历数据

```java
response = $.response;
if (response != null && response instanceof List) {
    responseList = new ArrayList();
    for (i = 0; i < response.size(); i++) {  
        responseList.add(response.get(i).clone());  
    }
}
```

### 日期格式化

```java
formatter = new java.text.SimpleDateFormat("yyyy-MM-dd");
sdf = new java.text.SimpleDateFormat("yyyy/MM/dd HHmmss");

validFrom = $.response.get("validFrom");
if (validFrom != null) {
    date = sdf.parse(validFrom);
    $.response.put("validFrom", formatter.format(date));
}
```