# HTTP节点

[[toc]]

## 概述
逻辑编排目前支持ODH类型、Restfu类型两种接口。可以通过HTTP节点的配置面板的API选项进行切换。
![flow-node-http-all](/images/guide/flow/flow-node-http-all.png)

## 配置方式
### ODH类型
注意：请确保你的集成环境已经集成了对应的ODH。

配置、操作入下图所示，当选择了ODH的接口之后，系统会默认回填请求方法、请求地址、出入参数等基本信息。

<div class="tip custom-block" style="padding-top: 8px">

**注意：在URL这一配置界面后有一个刷新按钮，点击后， 会动态刷新最新的ODH出入参到你的界面上， 但是不影响未更改部分字段的已经配置好了的规则值，比如你使用一个odh的接口很久了，这个odh的接口有很多参数，你全部都配置了字段的映射、规则， 某一天odh多加了必填字段，如果你直接重新选择该接口，那么你需要重新再配置一遍繁琐的接口参数的规则， 这个时候你可以点击刷新， 界面上入参会多一个字段，你只需要绑定这个新增的字段的映射、规则即可， 其他未变更的字段无需重新配置映射、规则**

</div>


(`注意  ODH类型的接口的大部分信息来源于选择的接口在ODH的配置，这些信息是不支持修改的，但是其中有两个参数是需要用户手动配置, 参数解释如下`)

* Error Handling参数: 表示异常是否会继续，默认“停止”，即请求的接口报错， 整个flow停止。如果勾选了继续， 意味着就算接口报错， 节点也会继续运行
**(`这个时候通常通常是建议: 在HTTP的节点后面连接分支节点来判断处理不同的报错`)**
* Request Information参数: 接口请求参数，通用界面的闪电图标或者齿轮图标给对应的接口绑定具体的变量或者值。
![flow-node-http-odh](/images/guide/flow/flow-node-http-odh.png)

### Restful类型
可自由配置接口的地址、请求方式、入参结构、出参结构

* Application Name: 选择接口的数据源，数据源的来源是在[配置文档-数据源](./flow-datasource.md)一节中配置的。
* URL: 接口请求的相对地址。 最终的请求地址是由Application Name选择的数据源配置项 + 这里的相对地址构成。
* Method: 接口请求的方式。
* Error Handling:  表示异常是否会继续，默认“停止”，即请求的接口报错， 整个flow停止。如果勾选了继续， 意味着就算接口报错， 节点也会继续运行， 这个时候通常是需要在HTTP的节点后面连接分支节点来判断处理不同的报错。
* Request Information、Reponse Information: 定义接口的入惨、出参数结构。

**注意： 接口调用前， 建议明确目标接口的出、入参结构， 并在图形化中定义同样的出、入参结构，防止因结构对不上导致最终请求失败。**
![flow-node-http-restful](/images/guide/flow/flow-node-http-restful.png)
