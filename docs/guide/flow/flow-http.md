# HTTP节点

[[toc]]

## 概述
HTTP节点支持两种接口协议类型：

| 类型          | 特点                                                                 | 适用场景               |
|---------------|--------------------------------------------------------------------|----------------------|
| ODH类型       | 自动继承ODH平台接口定义，减少配置工作量                               | 企业内ODH集成环境      |
| Restful类型   | 完全自定义请求配置，灵活对接任意HTTP接口                             | 第三方系统对接        |

![HTTP节点类型选择](/images/guide/flow/flow-node-http-all.png)  
*图1：API类型切换界面*

## 配置指南

### 1. ODH类型接口配置
#### 前置条件
✅ 确保集成环境已对接ODH平台

#### 自动填充机制
选择ODH接口后，系统将自动填充：
- 请求方法（GET/POST等）
- 请求地址
- 出入参结构

![ODH接口配置](/images/guide/flow/flow-node-http-odh.png)  
*图2：ODH接口配置界面*

#### 关键功能说明
🔁 **接口刷新按钮**  
<div class="tip custom-block" style="padding-top: 8px">

**注意：在URL这一配置界面后有一个刷新按钮，点击后， 会动态刷新最新的ODH出入参到你的界面上， 但是不影响未更改部分字段的已经配置好了的规则值，比如你使用一个odh的接口很久了，这个odh的接口有很多参数，你全部都配置了字段的映射、规则， 某一天odh多加了必填字段，如果你直接重新选择该接口，那么你需要重新再配置一遍繁琐的接口参数的规则， 这个时候你可以点击刷新， 界面上入参会多一个字段，你只需要绑定这个新增的字段的映射、规则即可， 其他未变更的字段无需重新配置映射、规则**

</div>


> **典型场景**：当ODH接口新增必填字段时，无需重新配置全部参数，只需补充新增字段映射即可。

#### 特殊配置项
| 参数                | 说明                                                                 |
|---------------------|--------------------------------------------------------------------|
| Error Handling      | ▶ 停止（默认）：接口报错时终止流程<br>▶ 继续：报错后继续执行（需配合分支节点处理异常） **这个时候通常通常是建议: 在HTTP的节点后面连接分支节点来判断处理不同的报错** |
| Request Information | 通过闪电图标或者齿轮图标绑定动态变量                                        |

---

### 2. Restful类型接口配置
#### 核心配置要素
可自由配置接口的地址、请求方式、入参结构、出参结构

* Application Name: 选择接口的数据源，数据源的来源是在[配置文档-数据源](./flow-datasource.md)一节中配置的。
* URL: 接口请求的相对地址。 最终的请求地址是由Application Name选择的数据源配置项 + 这里的相对地址构成。
* Method: 接口请求的方式。

| 参数                | 说明                                                                 |
|---------------------|--------------------------------------------------------------------|
| Error Handling      | ▶ 停止（默认）：接口报错时终止流程<br>▶ 继续：报错后继续执行（需配合分支节点处理异常） **这个时候通常通常是建议: 在HTTP的节点后面连接分支节点来判断处理不同的报错** |
| Request Information | 通过闪电图标或者齿轮图标绑定动态变量        

**注意： 接口调用前， 建议明确目标接口的出、入参结构， 并在图形化中定义同样的出、入参结构，防止因结构对不上导致最终请求失败。**
![flow-node-http-restful](/images/guide/flow/flow-node-http-restful.png)