# 开始节点

[[toc]]

## 概述
开始节点用于定义流程的输入参数，支持三种参数类型：

| 参数类型       | 传输方式              | 典型应用场景                     |
|----------------|---------------------|--------------------------------|
| Body 参数      | 请求体                | 传输结构化数据/文件              |
| Query 参数     | URL查询字符串         | 过滤、排序、分页等操作           |
| Header 参数    | HTTP请求头           | 认证、内容协商、控制缓存等        |

## 配置指南

### Body参数配置
支持两种数据格式：

#### JSON格式
**适用场景**：RESTful API交互，传输结构化数据  
**配置方式**：通过可视化编辑器定义JSON结构  
![JSON请求体配置](/images/guide/flow/flow-node-start-json.png)  
*图1：JSON参数配置界面*

**特点**：
- 支持嵌套对象和数组
- 可以直接导入数据结构, 一键专程对应的图形化数据结构

#### Form-Data格式
**适用场景**：文件上传和表单提交  
**配置方式**：通过"+"按钮添加多个字段  
![Form-Data配置](/images/guide/flow/flow-node-start-formdata.png)  
*图2：Form-Data参数配置界面*

**字段类型**：
- 文本字段
- 数字字段
- 长整形字段
- 布尔字段
- 时间字段
- 文件字段

### Query参数配置
![Query参数配置](/images/guide/flow/flow-node-start-query.png)  
*图3：Query参数配置界面*

**配置要点**：
- 参数名需符合URL编码规范
- 支持设置必填/可选标记

### Header参数配置
**重要规范**：所有Header 字段在输入框中的名称必须小写  
![Header参数配置](/images/guide/flow/flow-node-start-header.png)  
*图4：Header参数配置界面*