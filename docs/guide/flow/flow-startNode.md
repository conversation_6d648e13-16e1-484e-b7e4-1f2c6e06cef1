# 开始节点
[[toc]]

## 概述
开始主要用于定义流程的接受参数，参数主要分以下几类:
* Body 参数: 用于传递请求的主体数据
* Query 参数: 附加在 URL 后，用于过滤、排序或标识资源
* Header 参数: 通过 HTTP 请求头传递元数据或控制信息，如认证、内容类型等

## 配置方式
### Body参数设置
Body参数类型分为两个部分: 
* json 类型: 传递结构化的键值对数据，适合现代 restful API 交互。常用于 POST、PUT、PATCH 请求中传输对象或数组数据。
* form-data 类型: 常用于文件和表单提交。
#### Json请求体
json请求体通过图形化的操作来定义请求结构
![flow-node-start-json](/images/guide/flow/flow-node-start-json.png)

#### Form-Data请求体
form-data请求体通过点击表格的“+操作”来新建多个平行的form-data参数
![flow-node-start-formdata](/images/guide/flow/flow-node-start-formdata.png)

### Query参数设置
![flow-node-start-query](/images/guide/flow/flow-node-start-query.png)


### Header参数设置
**注意: header参数是需要小写的**
![flow-node-start-header](/images/guide/flow/flow-node-start-header.png)
