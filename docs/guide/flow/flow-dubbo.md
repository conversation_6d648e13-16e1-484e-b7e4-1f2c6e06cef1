# DUBBO 节点

[[toc]]

## 概述
Dubbo 节点实现了对 Dubbo RPC 框架的集成，支持对 Dubbo 服务的泛化调用，主要功能包括：

- **服务发现**：通过 Zookeeper 注册中心自动发现服务
- **接口调用**：支持选择特定分组和服务接口
- **泛化调用**：无需依赖服务接口 JAR 包即可发起调用

![Dubbo节点功能示意图](/images/guide/flow/flow-dubbo-config.jpg)  
*图1：Dubbo节点配置界面*

## 配置指南

### 1. 基础配置
| 配置项          | 说明                                                                 | 示例值                  |
|----------------|---------------------------------------------------------------------|------------------------|
| ZK Config Name | 在[数据源配置](./flow-datasource.md)中预先配置的 Zookeeper 连接信息 | dubbo_prod_zk         |
| Class          | 服务接口全限定类名                                                     | com.example.UserService |
| Method         | 需要调用的方法名                                                      | getUserById           |

### 2. 参数配置
通过“+”号增加一组参数配置.

- 基础类型
- Request Class类型: 需要手动手动配置Request Clas, 并且需要手动配置请求参数的类型结构
