# 映射节点

[[toc]]

## 概述
映射节点(DataMapping节点)是整个逻辑编排中最为核心的节点, 在数据转上有着非常重要的作用。

**注意：在配置变量规则的时候, 采用的语法QlExpress, 可参考[QlExpreess语法](./flow-qlexpress)**

## 重要概念理解
在熟练使用映射节点之前需要先了解下映射节点的基本操作、运行原理：
* **通过定义结构+绑定结构参数的形式, 可以得到业务上常见的数据结构**
* **绑定参数的映射可能会有很多条配置规则, 但是最终运行结果是由映射规则的最后一个规则决定**
* **通过新增、删除、修改按钮可用来定义映射的目标结构**
* **通过齿轮图标可以配置复杂的规则**
* **通过闪电图标可以快速映射，区别于上面的齿轮操作, 快速映射只能生效一条规则**

结构操作示意图如下
![flow-datamapping-all](/images/guide/flow/flow-datamapping-all.jpg)

规则配置操作示意图如下
![flow-datamapping-allrules.jpg](/images/guide/flow/flow-datamapping-allrules.jpg)

## 配置方式
### 映射属性的基本数据类型
基本类型包括：String、Long、Integer、Number、Double、Float、Boolean、Datetime、Object、Array等类型，每种类型的基本赋值方法相同

### 基本属性配置方法
以return节点为例（DataMapping节点、Http节点的请求，Dubbo请求等schema的基本赋值配置方式相同）

如下图所示
![flow-datamapping-basic](/images/guide/flow/flow-datamapping-basic.png)

有种方式来配置赋值规则：
* 快速赋值
* 复杂赋值

#### 快速赋值
图中所示闪电按钮，用此按钮可以快速从流程上下文选择一个值赋值给当前字段
例如给Return节点的name字段赋值，点击闪电按钮，就可以从上下文中选择参数

点击ok以后，就可以将DataMapping节点的name参数赋值给Return节点的name

![flow-datamapping-fast](/images/guide/flow/flow-datamapping-fast.png)

#### 复杂赋值
假如不仅仅需要简单的映射赋值，而是需要进行一些运算，则可以使用复杂赋值。复杂赋值可以使用QLExpression脚本本身的运算能力，也可以使用我们提供一些规则进行运算。
点击齿轮按钮，进入规则配置界面

例如，将name转换成大写
**1、点击+号增加一个规则**
![flow-datamapping-eg1](/images/guide/flow/flow-datamapping-eg1.png)

**2、选择大小转换规则**
![flow-datamapping-eg2](/images/guide/flow/flow-datamapping-eg2.png)
这样就可以将DataMapping的name转换成大写以后赋值给Return节点的name
除了上述利用规则进行大小写转换，因为name是String类型，因此也可以利用String类型本身的方法toUpperCase()进行大写转换。
![flow-datamapping-eg3](/images/guide/flow/flow-datamapping-eg3.png)
执行以后，可以看到name被转换成了大写
![flow-datamapping-eg4](/images/guide/flow/flow-datamapping-eg4.png)

### 简化赋值配置方法
有些场景中，schema非常复杂、参数很多，如果一个一个参数去配置，则非常繁琐。
例如配置开户接口HTTP请求，请求参数非常多，层级也很深。如果每个参数都去配置赋值，很麻烦。这里可以利用Object和Array类型的配置特性来简化配置
![flow-datamapping-eg5](/images/guide/flow/flow-datamapping-eg5.png)

#### Object类型的配置特性
##### 自动赋值
当我们在Object类型的字段上配置了赋值，则Object字段的成员都会自动赋值。
例如在return节点的最顶层选择start节点的输出
![flow-datamapping-eg6](/images/guide/flow/flow-datamapping-eg6.png)
因为Return节点的name和age在start节点的body中存在相同名称的字段，那么start.body中name和age就自动赋值给return节点的name和age。这样就无需每个字段都去配置赋值。从下图可以看到执行后的结果：return节点的name和age取的是start节点的body中的name和age
![flow-datamapping-eg7](/images/guide/flow/flow-datamapping-eg7.png)
![flow-datamapping-eg8](/images/guide/flow/flow-datamapping-eg8.png)
这个特性可以和1.2中的复杂赋值操作结合使用。通过复杂赋值规则计算出一个Obejct类型的对象A，将这个对象赋值一个Object类型的字段B，那么B的成员的也会被A中相同名称的成员赋值。

##### Object的特性支持多层级
如果Object类型字段还包括Object类型的成员，Object类型的简化赋值特性依旧适用。

如图中所示，在return节点的顶层选Start.body进行赋值，只要Start.body层级和return节点的层级是一致的，那么info字段的成员email和phone也会被自动赋值。
![flow-datamapping-eg9](/images/guide/flow/flow-datamapping-eg9.png)
![flow-datamapping-eg10](/images/guide/flow/flow-datamapping-eg10.png)
执行流程可以看到return节点的info的确被赋值了
![flow-datamapping-eg11](/images/guide/flow/flow-datamapping-eg11.png)
![flow-datamapping-eg12](/images/guide/flow/flow-datamapping-eg12.png)

##### 手动覆盖
上述的简化配置只适用于层级一致且名称相同的情况。如果层级结构或者名称不同，则需要手动赋值。

如果仅仅是Object这一层名称不同，就手动赋值这一层
![flow-datamapping-eg13](/images/guide/flow/flow-datamapping-eg13.png)
![flow-datamapping-eg14](/images/guide/flow/flow-datamapping-eg14.png)
如果成员的名称不相同，则需要覆盖到成员这一层
![flow-datamapping-eg15](/images/guide/flow/flow-datamapping-eg15.png)
最后return的phone的赋值表达式的如下图
![flow-datamapping-eg16](/images/guide/flow/flow-datamapping-eg16.png)
**手动赋值的优先级要高于自动赋值，因此可以使用手动赋值实现某个字段特殊的赋值要求。适用于大多数字段名称相同，只有个别字段名称不同或者有特殊要求的场景**

##### Return节点Object特性的区别
Object赋值特性DataMapping节点上和其他节点有些许不同。主要是两种模式的不同：
* 融合模式
* 覆盖模式


#### Array类型的配置特性
同时由于start节点传递的参数层级结构和参数名称基本是相同的

## 常见映射规则说明

### 基础映射规则
* variable: 映射变量，将写入的内容绑定的变量值赋值给参数

![flow-datamapping-eg17](/images/guide/flow/flow-datamapping-eg17.png)

![flow-datamapping-eg18](/images/guide/flow/flow-datamapping-eg18.png)

![flow-datamapping-eg19](/images/guide/flow/flow-datamapping-eg19.png)
* constant: 映射常量，将写入的内容作为常量赋值给参数

![flow-datamapping-eg20](/images/guide/flow/flow-datamapping-eg20.png)

![flow-datamapping-eg21](/images/guide/flow/flow-datamapping-eg21.png)
* To 数据类型（Double, Integer, Float, String）: 映射数据类型，将参数值转换成指定数据类型
* Max, Min, Sum: Max, Min, Sum映射
* Is Match:数据规则映射，是否匹配正确的正侧表达式
### 数组映射规则
* Array Sort: 数据排序

![flow-datamapping-eg22](/images/guide/flow/flow-datamapping-eg22.png)
* Filter Array: 数组过滤

![flow-datamapping-eg23](/images/guide/flow/flow-datamapping-eg23.png)
* List Concat: 数组合并

![flow-datamapping-eg24](/images/guide/flow/flow-datamapping-eg24.png)

![flow-datamapping-eg25](/images/guide/flow/flow-datamapping-eg25.png)
* Array To Map: 数组转Map

![flow-datamapping-eg26](/images/guide/flow/flow-datamapping-eg26.png)
* Field Value Array: 数组生成单值数组
* Array Duplication: 数组去重
### 时间映射规则
* Date Offset: 时间运算
* Filter Array: 时间差获取
* Date Format: 时间格式化
* Date Compare: 时间比较
### 字符串映射规则
* Substring: 截取字符串
* To LowerCase: 小写转换
* To UpperCase: 大写转换
* Trim: 字符串去除空格
* Replace First: 字符串替换第一个
* Replace All: 字符串替换所有
* Random: 随机生成字符串















