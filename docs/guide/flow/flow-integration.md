# 逻辑编排-集成文档

[[toc]]

## 概述

逻辑编排有3种态：

* **编辑态**: 提供可视化的流程编辑和调试
* **运行态**: 以Restful接口调用执行已编辑好的流程
* **sdk态**: 以sdk的方式执行编辑好的流程

三种态能力分层：编辑态包含运行态，运行态包含sdk态



![flow-intergration-threeState](/images/guide/flow/flow-intergration-threeState.png)



根据项目实际的需要引入各个态，不同的态对应不同的maven依赖。

## 环境支持

### JDK支持度

要求的最低的JDK版本为8
特别需要注意的是，如果你使用JDK9及其以上的版本，请确保jvm参数加上以下参数

```java
--add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
```

### 后端框架支持度

编辑态、运行态以及SDK态都支持core92、core93和core94版本的框架

|  框架版本  |      逻辑编排版本       |
| :----: | :---------------: |
| core92 |   9.2-SNAPSHOT    |
| core93 | 9.2(9.3)-SNAPSHOT |
| core94 |   9.4-SNAPSHOT    |



运行态以及SDK态支持8.2、8.4版本的框架

|  框架版本  |      逻辑编排版本       |
| :----: | :---------------: |
| core82 | 9.2(9.3)-SNAPSHOT |
| core84 |   9.4-SNAPSHOT    |

**说明:**  逻辑编排版本即各个态Maven依赖的版本，即集成方式一章中所述的easycode.version

**注意:**  不支持core81版本的框架



## 前端集成

编辑态集成涉及前端页面，前端界面集成有两种方式。

### 方式一 目录管理集成+内页集成

::: warning 开发态、runtime集成

**前端编辑器集成地址:** /easycode/index.html?locale=en-US&moduleId=64#/flowManagement

| 参数       | 说明                                 | 是否必传 |
| -------- | ---------------------------------- | ---- |
| locale   | 语言国际化(目前支持中英文 en-US、zh-CN) 默认是显示英文 | 否    |
| moduleId | 应用级别的平台集成 目前只在二次开发平台集成的时候需要传递      | 否    |

:::

### 方式二 内页集成

::: warning 开发态
**前端编辑器集成地址:** /easycode/index.html?flowId=flowId&showTabList=customized&backToPreviousText=Back to Previous#/singleFlowDesign
**前端编辑器的集成地址参数说明**

| 参数                 | 说明                                       | 是否必传 |
| ------------------ | ---------------------------------------- | ---- |
| flowId             | 逻辑编排的id                                  | 是    |
| showTabList        | 控制内页左上角的Logo是否需要返回, 不传 点击Logo无返回,传showTabList=customized,则显示返回 | 否    |
| backToPreviousText | 如果用showTabList开启了内页返回,想进一步控制返回按钮的文字,可传递参数 | 否    |

:::



## 后端集成

### 编辑态
编辑态提供界面进行流程编辑、调试。

由于编辑态包含运行态，所以也支持以restful接口调用执行流程



#### maven依赖
```java
<dependency>
      <groupId>com.iwhalecloud.easycode</groupId>
      <artifactId>easycode-design-tool</artifactId>
      <version>${easycode.version}</version>
</dependency>
```

#### 数据源配置
数据源支持单数据源，也支持多数据源。支持mysql和oracle。

- 单数据源：在各应用自己的数据库中加逻辑编排的表

- 多数据源：给逻辑编排提供单独的数据库

多数据源需要在配置文件中增加如下配置，以mysql数据库为例：
```sql
# 多数据源
phoenix.datasource.model=multiple

phoenix.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver
phoenix.datasource.druid.url=****************************************************
phoenix.datasource.druid.username=cdp_dev
phoenix.datasource.druid.password=cdp_dev@123
phoenix.datasource.druid.encrypted=false

# 数据源的其它配置项不一一列出，有需要配置加上phoenix.前缀即可

mybatis.configuration.database-id=mysql
```

oracle数据库需要额外配置mybatis的database-id
```sql
mybatis.configuration.database-id=oracle
```





### 运行态
运行态以Restful接口调用执行已编辑好的流程。

运行态包含两种模式：

- 本地文件模式
- 数据库模式

**本地文件模式:**  运行不依赖数据库，而是从特定目录下读取流程文件来执行流程。适用于不依赖数据库的场景，通常在编辑态下载编排的流程文件，然后提交到项目代码仓库的特定目录下。如果编辑态和cdp集成也支持直接将流程文件推送到代码仓库。

**数据库模式:**  从数据库中读取流程配置来执行流程。适用于编辑后需要立即生效，但是编辑态和运行态分开部署的场景。



#### maven依赖

本地文件模式

```java
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-runtime-engine</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



数据库模式

```java
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-runtime-engine-db</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



#### 本地文件模式文件路径

本地流程文件默认从zsmart_home下etc/flow/data路径中读取。此路径支持可配，配置项如下：

```java
phoenix.flow-location=${ZSMART_HOME}/etc/flow
```

配置上述配置项后，就会从${ZSMART_HOME}/etc/flow/data目录下查找流程文件。

**注意:**  配置项不需要配置data目录这一层，data目录是固定的



#### 流程执行url

运行态是通过调用restful接口来执行流程的，每个流程的url可以有三种方式查看：

1. 编辑内页界面的顶部信息图标中查看

   ![flow-intergration-runtime-url-1](/images/guide/flow/flow-intergration-runtime-url-1.png)

2. 在目录管理页面 => 悬浮卡片 => 点击Api detail中查看 

   ![flow-intergration-runtime-url-2](/images/guide/flow/flow-intergration-runtime-url-2.png)

3. 流程文件中查看

   流程文件可以查看url，不能查看入参和出参结构。文件的url的只包含变化的部分，不包含固定部分（/easycode/api/execute/），拼接固定部分组成完整的url：/easycode/api/execute/testAddDemoTableData

   ![flow-intergration-runtime-url-3](/images/guide/flow/flow-intergration-runtime-url-3.png)

### SDK态

sdk态不是通过接口调用来执行流程，而是通过sdk的方式来执行流程。

sdk态的三种模式：

- 本地文件模式：暂不支持
- 数据库模式：暂不支持
- 第三方文件模式：适用于项目上自行管理文件的场景

#### maven依赖
```java
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-core</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



#### SDK态的调用方法

##### 第三方文件模式

sdk方法：com.iwhalecloud.easycode.core.utils.ChainExeUtil#executeChain(java.lang.String, java.lang.String, com.iwhalecloud.easycode.core.entity.ChainHttpEntity)

```java
    public static Object executeChain(String chainKey, String el, ChainHttpEntity httpEntity) throws Exception {
        return executeChain(chainKey, el, httpEntity, null, null);
    }
```

参数说明：

1. chainKey：流程的唯一性key，由调用方保持唯一。
2. el：流程的配置文件内容
3. httpEntity：流程的入参，和调用restful一样，包含如下几个部分
   - body：对应http的请求body
   - header：对应http的请求header
   - queryParam：对应http的请求query参数
   - pathParam：对应http的请求path路径参数
   - file：文件上传的传参



## 插件集成

逻辑编排提供很多的插件，包括dubbo、mq、事务、接口安全等。

![flow-intergration-plugins](/images/guide/flow/flow-intergration-plugins.png)

插件分为两类：

- 功能性插件：用于提供某种功能，例如安全security插件用于提供http请求的安全认证；transaction则用于事务控制。
- 节点类插件：用于提供某种可拖拽的节点，例如dubbo节点，mq节点，以及脚本节点java、python等。

项目根据实际实际需要引入插件，例如项目上需要使用dubbo节点调用dubbo接口，那么就需要引入dubbo插件；如果需要发送mq消息，则引入mq插件。



默认集成的插件：

- transaction：用于事务控制
- response：用于配置流程的响应结构
- http-odh：用于http节点选择odh数据源
- http-other：用于http节点选择other数据源
- python：python脚本节点
- java：java脚本节点
- javascript：javascript脚本节点
- qlExpress：QLExpress脚本节点

如果想包含http-odh，http-other数据源或者response，则使用配置项phoenix.plugins.flow.exclude排除

```
phoenix.plugins.flow.exclude=http-other
```



### dubbo插件

提供dubbo接口的泛化调用功能

**设计态插件**

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-dubbo-design-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```

**运行态和sdk态插件**

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-dubbo-runtime-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



### mq插件

提供mq消息的发送功能

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-mq-runtime-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



### 安全插件

使用restful接口调用流程时，提供安全认证功能。包括Basic、Bearer Token、API Key等认证方式，也包括Session+Cookie的会话认证方式。

sessionCookie节点插件和安全认证是一起使用的，所以由同一个maven依赖引入。

#### **设计态**

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-security-design-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



#### **运行态**

运行态数据库模式

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-security-runtime-db-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```

非数据库模式

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-security-runtime-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```

**注意**：安全插件仅针对通过restful接口调用流程的情况，所以sdk态引入此插件不会有任何作用



#### 启用安全校验

默认启用安全校验，如果不想启用安全校验，有两种方式：

- 不引入安全插件
- 关闭安全校验配置项

```
phoenix.security.enabled=false
```



### 工作流插件

提供工作流的操作，目前支持phx-workflow工作流

**设计态插件**

```
<dependency>
  <groupId>com.iwhalecloud.easycode</groupId>
  <artifactId>easycode-workflow-design-plugin</artifactId>
  <version>${easycode.version}</version>
</dependency>
```

**运行态和sdk态插件**

```
<dependency>
  <groupId>com.iwhalecloud.easycode</groupId>
  <artifactId>easycode-workflow-runtime-plugin</artifactId>
  <version>${easycode.version}</version>
</dependency>
```





### 脚本插件

已经默认集成了python、java、javascript和qlExpress脚本插件节点，无需额外引入插件

如果需要引入其他的脚本类型的插件，则引入额外的maven依赖

groovy脚本

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-script-groovy</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



aviator脚本

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-script-aviator</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



kotlin脚本

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-script-kotlin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



lua脚本

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-script-lua</artifactId>
    <version>${easycode.version}</version>
</dependency>
```





### 应用级配置插件

提供全局正常和异常响应结构的配置。此插件针对restful接口调用流程的场景，对sdk态无效。

**设计态插件**

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-appconfig-design-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```



**运行态插件**

运行态数据库模式

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-appconfig-runtime-db-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```

非数据库模式

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-appconfig-runtime-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```

**注意**:  安全插件仅针对通过restful接口调用流程的情况，所以sdk态引入此插件不会有任何作用



### 国际化规则插件

此插件用于在return、dataMapping等节点表达式中提供一个国际化规则。此规则根据用户配置的异常码获取用户项目中的国际化资源。

```
<dependency>
    <groupId>com.iwhalecloud.easycode</groupId>
    <artifactId>easycode-i18n-rule-plugin</artifactId>
    <version>${easycode.version}</version>
</dependency>
```

**注意**:  目前此规则仅仅适用于V9项目



## 配置项

| 配置项                               | 功能                                       | 示例                                       |
| :-------------------------------- | ---------------------------------------- | :--------------------------------------- |
| phoenix.language.supported        | 支持的国际化语言                                 |                                          |
| phoenix.datasource.model          | 数据源模式，配置multiple表示多数据源，默认不配置是单数据源，和项目上共用数据库。多数据源模式需要配置phoenix单独的数据源。参考：编辑态>>后端集成>>数据源配置 | phoenix.datasource.model=multiple        |
| mybatis.configuration.database-id | 支持oracle和mysql两种值，项目上根据使用的数据库选择对应值       | mybatis.configuration.database-id=mysql  |
| phoenix.flow-location             | 配置流程文件的存放位置，不配置默认是${ZSMART_HOME}/etc/flow | phoenix.flow-location=${ZSMART_HOME}/etc/flow |
| phoenix.restCmp.defaultDomain     | http节点默认的请求url                           | phoenix.restCmp.defaultDomain=http://*************:9000 |
| phoenix.inst-record-enabled       | 是否记录流程执行实例，默认为false                      | phoenix.inst-record-enabled=false        |
| phoenix.inst-record-type          | 流程实例记录的方式，支持同步：sync和异步：asyn两种，目前仅支持同步，默认同步 | phoenix.inst-record-type=sync            |
| phoenix.record-context-enabled    | 是否记录完整的上下文，默认false，配置true，则每个节点执行结果都会记录完整上下文 | phoenix.record-context-enabled=false     |
| phoenix.key-node-list             | 关键节点，关键节点一定会记录完整的上下文                     | phoenix.key-node-list=restCmp,dubboCmp   |
| phoenix.ai.apiKey                 | 鉴权key                                    |                                          |
| phoenix.ai.apiUrl                 | 模型url                                    | phoenix.ai.apiUrl=https://lab.iwhalecloud.com/gpt-proxy/v1/chat/completions |
| phoenix.ai.model                  | AI模型                                     | phoenix.ai.model=gpt-4.1                 |
| phoenix.zk.use.cache              | 默认为true，配置dubbo接口的数据源时候，使用启动缓存保存从zookeeper查询的接口信息 | phoenix.zk.use.cache=true                |
| phoenix.components.show           | 配置需要展示的节点，默认不需要配置                        | phoenix.components.show=subflowCmp,forCmp,branchCmp,returnCmp,restCmp,convertCmp,exterSubFlowCmp,parallelCmp |
| phoenix.security.enabled          | 是否启用安全校验，默认true                          | phoenix.security.enabled=true            |



## 预置脚本

<a href="/cdp/phoenix-doc/pdm/flow/mysql.rar" download="mysql.rar">下载Mysql脚本</a>

<a href="/cdp/phoenix-doc/pdm/flow/oracle.rar" download="oracle.rar">下载Oracle脚本</a>

