# 子流程节点

[[toc]]

## 概述

子流程节点是一种用于封装相对独立逻辑的特殊节点。它将一组相关逻辑集中在一个节点内，实现更好的模块化和代码组织。

**主要特性：**
- 子流程内的最后一个节点将作为整个子流程的返回值
- 子流程内部节点在子流程外部不可直接访问
- 提供逻辑封装和隔离的作用域

**注意**：与"外部子流程节点"的区别：
- 外部子流程节点是调用已实现的独立流程(Flow)
- 子流程节点是内联定义的逻辑块

![flow-subflow-all](/images/guide/flow/flow-subflow-all.png)

## 配置方式

当前子流程节点的配置仅支持"事务配置"选项。

**事务配置说明：**
- 用于控制子流程内的事务行为
- 详细配置请参考[事务配置文档](./flow-transaction.md)

**配置界面截图：**
![flow-subflow-config](/images/guide/flow/flow-subflow-config.png)