# MQ 节点

[[toc]]

## 概述

消息队列（MQ, Message Queue）是用于实现进程间或跨系统通信的中间件技术，提供异步通信和解耦能力。

### 常见消息队列系统
| 消息队列 | 主要特点 | 适用场景 |
|---------|--------|---------|
| RabbitMQ | 基于 Erlang，AMQP 协议 | 企业级消息路由 |
| ActiveMQ | Java 生态，JMS 协议 | 传统 Java 应用 |
| Kafka | 高吞吐，分布式 | 日志处理，流数据 |
| RocketMQ | 阿里开源 | 金融级，大数据场景 |

### 消息队列工作流程
1. **生产者(Producer)** 发送消息到消息队列
2. 消息队列暂存和路由消息
3. **消费者(Consumer)** 从队列获取并处理消息

## 支持要求

- 当前支持的 MQ 类型：**ZMQ**
- 未来计划扩展支持更多消息队列系统

## 配置说明

### 核心参数

| 参数 | 必填 | 说明 |
|------|-----|------|
| `topic` | 是 | 消息主题，用于消息分类 |
| `keys` | 否 | 消息关键属性标识 |
| `tags` | 否 | 消息标签，辅助分类 |
| `delayTimeLevel` | 否 | 消息延迟投递等级 |
| `sendMethod` | 是 | 发送方式：<br>- 同步<br>- 单向(oneWay)<br>- 异步(暂不支持)<br>*顺序生产者仅支持同步* |
| `shardingKey` | 否 | 顺序消息分片键 |
| `body` | 是 | 消息内容主体 |

![flow-mq-node](/images/guide/flow/flow-mq-node.png)

### 配置示例
典型 MQ 节点配置参考：
![flow-mq-node1](/images/guide/flow/flow-mq-node1.png)
