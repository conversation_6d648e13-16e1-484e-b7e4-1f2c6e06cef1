# MQ节点


[[toc]]

## 概述
消息队列（MQ, Message Queue）是一种进程间通信或不同应用系统间通信的中间件。

常见的 MQ 有：
RabbitMQ（基于 Erlang，AMQP 协议）
ActiveMQ（Java 生态，JMS 协议）
Kafka（高吞吐，分布式日志系统）
RocketMQ（阿里开源，适合大数据场景）

MQ 的基本流程：

生产者（Producer）**发送消息到消息队列。

消息队列暂存消息。

消费者（Consumer）**从队列中拉取/接收消息进行处理。

## 要求

MQ节点支持多种类型的mq，目前仅支持zmq

## 配置

* mq节点参数说明

    topic：主题，必填。

    keys: 关键属性，选填。

    tags：标签，选填。

    delayTimeLevel：延时时间等级，选填。

    sendMethod：发送方式，包括：同步、单向(oneWay)、异步(暂不支持)。如果数据源配置的是顺序生产者，则只有同步。
    
    shardingKey：顺序分片key。

    body：报文采集。

  ![flow-mq-node](/images/guide/flow/flow-mq-node.png)
* mq节点示例

  ![flow-mq-node1](/images/guide/flow/flow-mq-node1.png)
