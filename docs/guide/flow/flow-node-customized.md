# 自定义节点

[[toc]]

## 概述
当逻辑编排内置节点无法满足您的业务需求时, 您可以通过自定义节点功能进行扩展开发. 本指南将详细介绍如何开发一个完整的自定义节点, 包含前端和后端两部分的实现.

## 前端开发
前端的节点定制化代码, 目前是需要在产品分支里面进行定制化, 代码分支路径为, https://git-nj.iwhalecloud.com/crmr9/phoenix92-flow-designer.git

如果没有仓库权限,可以先申请仓库权限 
* 仓库组 Phoenix9.2
* 仓库名 phoenix92-flow-designer

### 定制前说明(非常重要)
::: tip 注意事项
项目的src文件夹下的customized-flow文件夹是存放项目定制化的需求,除此之外应该不涉及到改其他文件, 如涉及到或者有公共修改的需求, 可先我们联系, 确保对其他产品无影响才可以提交、发布
:::

### 定制步骤

**1、新增项目文件夹**
::: tip 新增项目文件夹
在customized-flow文件夹下新建一个项目文件夹(eg: AICC), 定制化项目的节点需求请放对应的定制化项目文件夹下
:::

::: tip 整体定制化项目结构
```bash
customized-flow/
└── 项目名称/
    ├── constants.ts    # 节点常量定义
    ├── nodesConfig.ts  # 节点注册配置
    └── nodes/          # 节点实现
        └── demoNode/   # 示例节点
            ├── config.ts  # 节点配置
            ├── index.tsx  # 组件导出
            ├── node.tsx   # 节点展示组件
            ├── panel.tsx  # 配置面板
            ├── type.ts    # 类型定义
            └── service.ts # API服务
└── index.tsx #定制化组件导出文件 

```
:::

**2、节点定制**
::: tip 步骤一 常量定义
定制化项目文件(eg: AICC)下新建constants.ts文件(节点常量文件)、nodesConfig.ts(节点面板、配置展示组件文件、节点存放目录配置)
<details>
<summary>constants.ts 代码解释</summary>

```typescript
const NODETYPES = {
  AICC_SCENE: { // 定制化节点A
    NODECOMPONENT: 'aiccSceneCmp', // 和后台一一对应的组件el名 后台组件注册的时候用的也是这个名称
    NODETYPE: 'ChatScene', // 组件类型名称 用来组件被拖拽出来之后显示在画布上的名称
    NODEUNIQUEID: 'AICC_SCENE_ID', // 唯一ID 必须要
  },
  AICC_ACTION: { // 定制化节点B
    NODECOMPONENT: 'aiccActionCmp',
    NODETYPE: 'ChatAction',
    NODEUNIQUEID: 'AICC_ACTION_ID',
  },
};
export { NODETYPES };
```
</details>

<details>
<summary>nodesConfig.ts 代码解释</summary>

```typescript
import type { IItemINodeComponentMap, INodeComponentMap, INodeComponentMapCategory } from '@/flow/nodes/nodesConfig';
import { NODETYPES } from './constants';
import AiccScene from './nodes/aiccScene'; // 定制化节点A的文件 这个往后看 后面会说明
import AiccAction from './nodes/aiccAction';  // 定制化节点B的文件 这个继续往后看 后面会说明

// 自定义的目录类型 用于存放节点在界面上显示在哪个目录值下
type INodeComponentMapCategoryAICC = INodeComponentMapCategory & {
  AI: Array<IItemINodeComponentMap>;
};

const NodeComponentMap: INodeComponentMap = {
  // AiccScene节点
  [NODETYPES.AICC_SCENE.NODETYPE]: {
    component: AiccScene.AiccSceneNode,        // 节点A 节点展示
    componentPanel: AiccScene.AiccScenePanel,  // 节点A 节点配置面板展示
    nodeConfig: {
      ...AiccScene.AiccSceneConfig,             // 节点A的图标、颜色等等配置项
      nodeType: NODETYPES.AICC_SCENE.NODETYPE,  // 节点A的类型名称
    },
  },
  // AiccAction节点
  [NODETYPES.AICC_ACTION.NODETYPE]: {
    component: AiccAction.AiccActionNode,
    componentPanel: AiccAction.AiccActionPanel,
    nodeConfig: {
      ...AiccAction.AiccActionConfig,
      nodeType: NODETYPES.AICC_ACTION.NODETYPE,
    },
  },
};

const NodeComponentMapCategory: INodeComponentMapCategoryAICC = { // 定制化节点的显示目录 单个目录下可以显示多个节点
  AI: [NodeComponentMap[NODETYPES.AICC_SCENE.NODETYPE],
    NodeComponentMap[NODETYPES.AICC_ACTION.NODETYPE]],
};

export { NodeComponentMap, NodeComponentMapCategory }; // 导出节点配置和目录配置即可

```
</details>
:::



::: tip 步骤二 节点逻辑定制化
创建nodes文件夹, 用于存放定制的节点文件(eg. AICC/nodes), 下属每一个文件夹对应一个节点, 每一个节点应该有下面的这个文件

**1、config.ts 用于配置节点的图标、展示组件、校验规则、el参数等配置等**
<details>
<summary>config.ts 代码解释</summary>

```typescript
import { NODETYPES } from '../../constants'; // 这个是上面定义的常量
import { renderScope } from '@/flow/utils'; // 固定写法
import { validateNodeConnected, validateNodeIdEmpty } from '@/flow/nodes/commonValidate'; // 固定写法
import type { IBaseNode, IValidateNodeError } from '@/flow/nodes/type'; // 固定写法

const AiccActionConfig: IBaseNode = {
  iconfont: 'icon-jiqiren', // 节点展示的图标名称 是一个iconfont图标库,请先注册一个iconfont账号,注册完成之后再找我们加图标库的权限
  style: {
    backgroundColor: 'rgb(0, 123, 255)', // 图标的背景色
  },
  componentDescription: 'Call AI Action',  // 节点的功能描述
  nodeComponent: NODETYPES.AICC_ACTION.NODECOMPONENT,// 这个是在上面第2大步定义的
  nodeUniqueId: NODETYPES.AICC_ACTION.NODEUNIQUEID,// 这个也是在上面第2大步定义的
  validateNode: ({ currentNodeCollation, edges, id }) => {
  // validateNode这个是一个自定义的校验函数,currentNodeCollation里面可以拿到在节点逻辑中塞进去的数据, 通过判断数据来校验节点哪里未通过校验, validateErr这个字段都会显示在画布下放工具栏的checkList中, 用于提示节点哪里有问题
    const { actionId } = currentNodeCollation;

    return new Promise((resolve) => {
      // 校验 固定写法 可以保留
      const validateErr: IValidateNodeError = {
        collationData: currentNodeCollation,
        validateErrors: [
          ...validateNodeIdEmpty({
            nodeCollation: currentNodeCollation,
          }),
          ...validateNodeConnected({
            id: id || '',
            nodeCollation: currentNodeCollation,
            edges: edges,
          }),
        ],
      };
    // 举个例子 这一块是个定制化逻辑
      if (!actionId) {
        validateErr.validateErrors.push({
          errorMessage: 'No action was found',
        });
      }
      resolve(validateErr);
    });
  },
  transferElParams: ({ currentNodeCollation, collationNodesData, id }) => {
  // transferElParams 最终return出去的是就是后端能拿到的结果值
    const { nodeId, actionId, inputContent } = currentNodeCollation;

    return new Promise((resolve) => {
      // el params
      resolve({
        name: nodeId, // 固定写法
        actionId, // 举个例子 某个节点的定制化逻辑
        inputContent,// 举个例子 某个节点的定制化逻辑
        ...renderScope(id, collationNodesData), // 固定写法
      });
    });
  },
};

export default AiccActionConfig;

```
</details>

**2、index.tsx 用于导出组件的node展示态组件、编辑面板展示态组件、组件的配置文件**

<details>
<summary>index.tsx 代码解释</summary>

```typescript
import AiccActionConfig from './config'; // 这个是上面定义的config.tsx的配置文件
import AiccActionNode from './node'; // 节点的node展示 往后看 下面会说到
import AiccActionPanel from './panel'; //  节点的panel面板展示 往后看 下面会说到

export default {
  AiccActionPanel,
  AiccActionConfig,
  AiccActionNode,
};

```
</details>

**3、node.tsx、panel.tsx分别对应节点在画布下的展示组件、编辑面板下的展示组件, 具体的组件业务实现请在这两个文件中实现**
<details>
<summary>node.tsx 文件解释</summary>

```typescript
import { useState } from 'react';
import { useModel } from 'umi';
import { IBusinessNodeProps } from '@/flow/nodes/type'; // 固定写法

const AiccActionNode: React.FC<IBusinessNodeProps> = (props) => {
  const { id } = props; // 节点的id
  const { collationNodesData } = useModel('collationData'); // 获取当前节点的业务数据

  return (
    <div>我是在node中显示的</div>
  );
};

export default AiccActionNode;

```
</details>


<details>
<summary>panel.tsx 文件解释</summary>

```typescript

import { IBusinessNodeProps } from '@/flow/nodes/type';
import React, { useState } from 'react';
import { useModel } from 'umi';
import { IAiccAction } from './type'; // 当前节点对应的ts文件

const AiccActionPanel: React.FC<IBusinessNodeProps<IAiccAction>> = (props) => {
  const { id } = props;
  const { collationNodesData, updateNodeToCollation } = useModel('collationData');

  
    // updateNodeToCollation(id, { // updateNodeToCollation这个函数用来将当前的这个配置面中发生变更的配置数据更新到对应节点的业务数据中
    //   fieldAAA: 'XXXX'
    // });


  return (
   <div>我是在节点的配置面中显示</div>
  );
};

export default AiccActionPanel;

```
</details>

**4、其他文件夹type.ts、services.ts按组件的需要进行添加**

<details>
<summary>type.ts 代码解释</summary>

```typescript
interface IAiccAction { // 用于定义这个定制化节点的业务数据中有哪些字段
  actionId: number;
  inputContent: string;
}

export type { IAiccAction };

```
</details>

<details>
<summary>service.ts 代码解释</summary>

```typescript
import { request } from 'umi';

// 举个例子: 这里没有什么特殊的 正常定义接口接口
export async function qryActionListByPagetip(param: any, filter: IFilter) {
    // return request(`../easycode/assistant/api/action/qryActionListByPagetip`, {
    //     method: 'GET',
    //     params: {
    //         ...param,
    //         ...filter
    //     }
    // });
}


```
</details>

:::

**3、组件导出**
::: tip 组件导出
在customized-flow/index.tsx中导出自己的定制化组件即可

<details>

<summary>导出组件代码示例</summary>

```typescript
import {
  NodeComponentMap as AICC_NodeComponentMap,                     //  nodesConfig中定义的第一个配置项
  NodeComponentMapCategory as AICC_NodeComponentMapCategory,     //  nodesConfig中定义的第二个配置项
} from '@/customized-flow/AICC/nodesConfig';

// 定制化组件第一个修改的地方 
const NodeComponentMap = {
  ...AICC_NodeComponentMap,
};

// 定制化组件第二个修改的地方
const NodeComponentMapCategoryALL = [AICC_NodeComponentMapCategory];

export { NodeComponentMap, NodeComponentMapCategoryALL };


```
</details>

:::

**4、注意事项**
::: tip 注意事项
界面上如果想看到自己刚刚配置的这一套组件 = 前端代码注册配置完之后 + 后端的节点注册
:::

**5、项目启动调试**
::: tip 项目启动调试
* 自定义节点前端与后端联调时, 如果定制化组件涉及到接口的请求, 前端的接口在.umirc.ts文件的proxy中配置转发
* **注意:** 联合调试时候的转发配置文件不要提交! 不要提交! 不要提交！只需要提交自己定制化那块的逻辑即可!
:::






## 后端开发

目前仅支持自定义普通节点，不支持项目化branch、for等逻辑控制节点。

### 定制步骤

项目化节点分3步：

1. 新增节点类：继承PhoNodeComponent类型
2. 节点类加注解：@PhoCmpPlugin(name = "customizedCmp")，名称不要和其它节点重复
3. 实现deal方法：deal方法实现自定义业务逻辑

实现示例如下

```java
import com.iwhalecloud.easycode.core.serializer.util.JsonUtil;

@PhoCmpPlugin(name = "customizedCmp")
public class CustomizedCmp extends Exception {

    @Override
    public Object deal() throws Exception {
        // 获取组件上下文信息
        JSONObject context = this.getPhoContext(this);

        // 获取组件结构数据
        CustomizedCmpParam csd = this.getPhoStructureData(this, CustomizedCmpParam.class);

		// 执行表达式获取前面节点执行的结果
		Object otherNodeResponse = RuleUtils.executeRuleExpression(csd.getQLExpress(), context, null, null);

		// 实现业务逻辑
		Object response = doBussiness();
      
      	// 返回值将被转成JSON，并设置到上下文中
        return response;
    }
}
```







自定义节点能够从上下文中获取已经执行节点的结果，也可以将自己的执行结果设置到上下文中。

PhoNodeComponent中提供了很多方法帮助实现自定义节点的功能。常用的就是获取上下文和获取节点配置参数。

### 获取上下文

```java
JSONObject context = this.getPhoContext(this);
```

此方法可以获取完整的上下文，包含了所有节点的执行结果。



### 获取节点配置参数

```
CustomizedCmpParam csd = this.getPhoStructureData(this, CustomizedCmpParam.class);
```

CustomizedCmpParam这个类是自定义的。其字段需要前端保存的节点配置参数保持一致。

以dubbo节点为例，前端配置参数为：

![flow-customized-param](/images/guide/flow/flow-customized-param.png)



后端节点配置类

```java
public class DubboCmpParam {
    private String dataSourceName;

    private String interfaceClass;

    private String methodName;

    private String[] parameterTypes;

    private List<JSONObject> schemas;

    private JSONObject responseSchema;

}
```



### 获取其它节点执行结果

节点执行结果在context中的key为界面上用户配置的节点名称。如果想获取某个节点的执行结果，通常需要界面编排流程的时候选择其它节点的执行结果，并将这种选择以QLExpress表达式形式保存在流程参数中，这一点参考自定义节点前端开发。

节点类中通常通过执行表达式来获取到其它节点的执行结果。执行表达式使用如下方法：

```
Object preNodeResponse = RuleUtils.executeRuleExpression(qlExpress, context, null, null);
```

说明：

- qlExpress：QLExpress表达式，一个字符串。这个表达式从可以从前端节点配置参数中获取。
- context：上下文



如果仅仅只是去上一个节点的执行结果，通过如下方式获取：

```java
Object preNodeResponse = context.get(Constants.RESPONSE);
```

但是这种获取前一个节点的执行结果的方式，在存在并行节点的场景下谨慎使用。因为如果有并行节点处理并行任务，这种方式获取结果无法确定到底是哪个节点的结果。如果项目使用时并不关心这这一点，则可以继续使用。



### 请求schema

有些自定义节点，可能需要不是仅仅一个前面节点执行结果，而是需要很多的节点执行结果的集合。针对这种场景，节点可以通过配置请求schema来实现，schema可以添加字段，并将字段和其他节点的执行结果映射。后端代码中可以使用工具执行这个schema，得到一个映射的实际结果。

还是dubbo节点为例

前端界面上schema的形式

![flow-customized-request-schema](/images/guide/flow/flow-customized-request-schema2.png)



保存后el文件中的形式

![img](/images/guide/flow/flow-customized-request-schema.png?lastModify=1752139252) 

其中offerId的$.Start.body.offerId即为QLExpress表达式，这种方式就会将Start节点的body参数中的offerId，映射到当前dubbo节点请求schema的offerId。

**注意**：el文件中，schema不一定要使用schemas字段名称，这个可以自定义，只需要和DubboCmpParam类中的字段名称能映射上就可以。也不一定数组，这个根据节点的实际需要而定，dubbo节点之所以是数组，是因为dubbo请求可能包含多个请求参数。



后端代码中使用RuleProcessor执行schema获取实际的结果

```java
// 获取组件结构数据
DubboCmpParam csd = this.getPhoStructureData(this, DubboCmpParam.class);

List<JSONObject> schemas = csd.getSchemas();

// 创建规则处理器实例
RuleProcessor ruleProcessor = new RuleProcessor(context, SchemaType.REQUEST);

// 组装参数
List<Object> invokeParams = new ArrayList<>();
// 应用规则处理schema
for (JSONObject schema : schemas) {
    Object schemaObj = ruleProcessor.applyRule(schema);
    invokeParams.add(schemaObj);
}
```



### 响应schema

一个节点通常有执行结果需要放到上下文中，而响应schema描述的即响应结构以及字段类型。其在后端的中主要作用是用于将响应的JSON转换成其字段定义的类型，避免界面定义的类类型和实际的类型不一致，导致后续使用中出现问题。

节点展示的响应schema如图所示

![flow-customized-response-schema2](/images/guide/flow/flow-customized-response-schema2.png)



下面以dubbo节点为例展示如何使用

响应schema和请求的schema一样，最终也是由前端保存在el文件中。

![flow-customized-response-schema](/images/guide/flow/flow-customized-response-schema.png)

**注意**：el文件中，responseSchema不一定要使用responseSchema字段名称，这个可以自定义，只需要和DubboCmpParam类中的字段名称能映射上就可以。



后端代码从DubboCmpParam获取响应的shema，然后将节点的json结果转换成其字段定义的类型。

```java
// 获取组件结构数据
DubboCmpParam csd = this.getPhoStructureData(this, DubboCmpParam.class);

// 获取响应schema信息
JSONObject responseSchema = csd.getResponseSchema();

// 省略其他逻辑

// 调用dubbo接口
Object responseObject = DubboGenericUtil.genericInvoke(zkAddress, interfaceClass,
                methodName, serviceGroup, parameterTypes, invokeParams.toArray(), timeOut, retries, loadBalance);

// 使用此工具类将响应结果转换成响应schema定义的类型
responseObject = ContextUtil.transferParamForContext(responseSchema, responseObject);

// 最后将这个responseObject返回设置到上下文中
return responseObject;
```




