# 数据源配置

[[toc]]

## 概述

数据源配置，目前支持三种类型的数据源配置，配置的数据将在以下流程编排中的以下三个节点配置时使用。数据源配置的位置如下图所示:
![flow-datasource-all](/images/guide/flow/flow-datasource-all.png)

## 配置方式

### REST数据源配置

界面配置参数解释如下
* Application Name: 当前配置项的名称
* Protocol: 协议+ IP, 请注意填写正确的IP, 此处的Protocol将会决定后续的HTTP节点在应用了这套配置之后， 接口往哪里请求。
* Port: 接口请求的端口
* Base Path: 如果你的项目接口有统一前缀的话， 那么每个HTTP节点就不需要写着一部分的路径， 只需要将通用的path路径配置这里即可 注意： Basic Path需要加上/
* Auth Type: 如果HTTP请求的目标接口有鉴权的话， 可在这里配置对应的鉴权请求信息

整体配置示例如下：
![flow-datasource-rest](/images/guide/flow/flow-datasource-rest.png)

### DUBBO数据源配置
界面配置参数解释如下:
* Name: 当前DUBBO配置项的名称
* Registry Address: 目标DUBBO的注册地址, 在填写了正确的地址之后， 界面会联动查询出可选择的Registry Group
* Registry Group: Registry Address被正确配置之后， 会联动自动查询可选择的选项
* Timeout: 超时配置项
* Retry Times: 重试次数
* Load Balance：负载均衡配置策略

整体配置示例如下：
![flow-datasource-dubbo](/images/guide/flow/flow-datasource-dubbo.png)

### MQ数据源配置
界面配置参数解释如下:
* Producer Group：生产者组，字符串类型。同时作为数据源名称。
* MQ Type：mq类型，目前默认仅支持zmq，后面还需要支持kafka，rabbitMq等多种类型。
* NameServer Address：​NameServer地址。
* Order Message：顺序消息，默认为false。
* Client Instance Name：客户端实例名称。
* Username：用户名。
* Password：密码。
* Timeout：发送消息的超时时间，该配置项对ZMQ、CtgMQ和AliMQ生效。选填项，单位是ms，ZMQ和AliMQ默认值为5000，CtgMQ为3000。
* TLS：是否开启TLS传输功能，ZMQ专属配置项，默认值是false。
* Queue Cache：是否开启队列缓存功能，ZMQ专属配置项，默认值为false，即不开启队列缓存功能。
* Namespace：namespace名称，ZMQ专属配置项，使用了多租户功能实现数据隔离的ZMQ集群需要配置该项。

整体配置示例如下：

  ![flow-mq-config1](/images/guide/flow/flow-mq-config1.png)
