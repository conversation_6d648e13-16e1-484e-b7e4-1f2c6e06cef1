# 响应结构配置

[[toc]]

## 概述

响应结构配置指的是Flow在被正确配置好后，运行时的返回结构。分为两个部分: 全局响应结构配置、单个Flow的响应结构配置。 全局响应结构配置和单个Flow的响应结构关系如下：
1. 全局响应结构定义好数据后， 单个Flow定义响应结构时: 选择Flow的响应状态码, 默认会出现对应的全局结构， 但是可以基于这个全局结构做二次修改
2. 当某个Flow响应结构已经定义好时， 再去修改全局结构， 这时的单个Flow响应结构是不会跟变的
3. **重要: 建议在大量新增逻辑编排之前先定义好项目的响应结构**

整体入口如下
![flow-datasource-all](/images/guide/flow/flow-response-all.png)

## 全局响应结构
### 通用配置
1. 配置项1  Response Wrapper：表示Flow最终返回的结构是否需要包裹上一层逻辑编排的数据结构，默认为不开启状态，如果开启了，那么错误响应结构会被包裹在data字段中返回， 默认包裹的数据结构如下所示:
{
    "succeed": true,
    "code": "00000",
    "message": "Success",
    "data": {},
    "timestamp": "2025-05-23 16:03:08:700",
    "solutionLink": null,
    "traceId": null
}
2. 配置项2 Unify Status：表示不论接口失败或者成功， http的status状态码始终会返回200，默认为不开启状态

#### 成功响应结构配置
针对的是http 状态码为20x类的结构定义， 通过图形化定义出所需要的成功响应结构

#### 错误响应结构配置
错误响应结构的配置相对于成功结构要稍微复杂点

1. 错误中的特殊字段配置:  定义当逻辑编排发生错误时(执行过程中的错误)， 比如请求方式不对、内部错误等等时候， 将错误字段映射到你自定义的错误字段当中去
举个例子：项目自己的返回中有一个字段叫做errorCode， 逻辑编排发现接口请求方式错误了， 这个时候你想让逻辑编排返回的errorCode为99999, 就按照如下配置
![flow-datasource-rest](/images/guide/flow/flow-response-error.jpg)

2. 错误其他结构配置 和成功结构配置一致，通过图形化操作界面

## Flow响应结构配置

Flow响应结构配置是最终影响逻辑编排返回结构的直接因素，通过给指定的状态码配置结构来影响最终的返回。
当你选择20X类的状态码的时候，默认会显示你上面配置的全局响应结构中的成功结构， 可以做二次修改
当你选择非20X类的状态码的时候，默认会显示你上面配置的全局响应结构中的错误结构， 可以做二次修改
 
**重要：修改完单个flow的响应结构保存的时候，会默认将画布上的return节点中对应状态码的结构刷新成新的结构，但是会保留未变更结构的映射配置。
举个例子：比如你给200结构加了一个testA的字段， 保存之后， 界面上status为200的return节点也会自动更新显示，其他配置了映射关系但是没改的字段， 依旧保持不动**

配置入口和界面如下所示:
![flow-datasource-dubbo](/images/guide/flow/flow-response-single.jpg)



