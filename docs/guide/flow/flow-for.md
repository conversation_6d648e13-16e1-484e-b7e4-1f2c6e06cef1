# 循环节点

[[toc]]

## 概述

循环节点是一种控制结构节点，用于重复执行指定的流程片段。它提供了对循环次数和事务行为的配置能力。

![flow-for-all](/images/guide/flow/flow-for-all.png)

## 配置方式

### 1. 迭代次数 (Times of Iteration)
- **功能**：控制循环执行的次数
- **取值方式**：
  - 直接填写固定数字（如：5）
  - 循环数组类型变量（如：`$.testArr.size()`）

### 2. 事务配置 (Transaction Configuration)
- **功能**：控制循环体内的事务行为
- **详细说明**：请参考[事务配置文档](./flow-transaction.md)

![flow-for-confgi](/images/guide/flow/flow-for-confgi.png)

## 使用说明

1. **循环体定义**：
   - 将需要重复执行的节点拖拽至循环节点内部
   - 循环节点会自动包含这些节点作为循环体

2. **变量访问**：
   - 循环体内可以访问外部变量
   - 每次迭代会创建独立的变量作用域
   - $.LoopStart代表循环开始节点的返回值，是一个对象，里面包含一个名为index的key值，表示当前的循环索引
   - 如果循环的是数据类的变量, 可以通过$.testArr[$.LoopStart.index]获取当前的循环项

3. **性能建议**：
   - 避免在循环体内执行耗时操作
   - 大数据量循环建议分批处理