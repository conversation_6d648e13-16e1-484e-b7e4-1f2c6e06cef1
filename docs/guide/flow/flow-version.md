# 版本控制

[[toc]]

## 概述
逻辑编排支持三种版本状态管理：
- **发布状态**（Released）：正式运行版本
- **草稿状态**（Draft）：开发调试版本  
- **废弃状态**（Retired）：已停用版本

不同状态对应不同的操作权限，界面示例如下：
![版本管理总览](/images/guide/flow/flow-version-all.png)  

## 版本管理操作指南

### 1. 新增版本
**操作路径**：基于现有版本创建新的版本  
![新增版本界面](/images/guide/flow/flow-version-add.png)  

特性说明：
- 支持选择任意历史版本作为基线
- 新版本自动进入`草稿(Draft)`状态


### 2. 发布版本管理
**可用操作**：
- ✅ 保存修改
- ✅ 下线版本（转为废弃状态）  

![发布版本操作界面](/images/guide/flow/flow-version-release.png)  

### 3. 草稿版本管理  
**可用操作**：
- ✅ 发布版本（转为发布状态）
- ✅ 保存修改  

![草稿版本操作界面](/images/guide/flow/flow-version-draft.png)  


### 4. 废弃版本管理
**限制说明**：
- ❌ 不可保存修改
- ❌ 不可重新发布
- ❌ 不可下线  
- ✅ 可以基于废弃版本新增一个新的草稿状态的版本

> **注意**：废弃版本建议不要开启任何针对废弃版本的操作, 避免废弃版本上线导致问题的发生.

## 版本状态转换图
```mermaid
stateDiagram-v2
    [*] --> 草稿状态
    草稿状态 --> 发布状态: 发布操作(通过发布按钮)
    发布状态 --> 废弃状态: 下线操作(通过下线按钮)
    废弃状态 --> [*]
```