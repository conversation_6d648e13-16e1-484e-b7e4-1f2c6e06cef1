# 逻辑编排简介

[[toc]]

## 什么是逻辑编排

逻辑编排是一款面向开发和业务人员的可视化逻辑编排工具，通过直观的拖拽式界面进行流程设计。用户无需深入编码即可实现条件判断、API 集成、数据转换等高级逻辑，大幅降低开发门槛，提升业务响应速度。

## 核心特性

### 拖拽式逻辑编排

通过直观的可视化界面连接预置模块，无需编码即可完成条件判断、循环控制、API调用等复杂逻辑链设计，有效降低技术门槛。

### 丰富的节点类型

支持多种类型的节点，包括：

- **脚本节点**：支持 JavaScript、Python、Groovy 等多种脚本语言
- **API节点**：HTTP请求、Dubbo调用等接口集成
- **控制节点**：分支判断、循环控制、并行处理
- **子流程节点**：支持流程复用和模块化设计

### 灵活的配置选项

提供完善的配置能力：

- **数据源配置**：支持多种数据源的连接和管理
- **安全配置**：接口访问控制和权限管理
- **全局配置**：统一的成功和错误处理机制
- **环境配置**：支持多环境部署和切换

### 全链路调试监控

- **可视化执行图谱**：每一步操作状态清晰可见
- **实时监控**：支持查看每个节点的执行结果和耗时
- **调试工具**：快速定位逻辑瓶颈和异常问题
- **日志追踪**：完整的执行日志记录和分析

## 适用场景

逻辑编排适用于以下业务场景：

- **业务流程自动化**：订单处理、审批流程、数据同步等
- **API编排集成**：多系统接口调用和数据整合
- **数据处理管道**：数据清洗、转换、验证等处理流程
- **条件业务逻辑**：复杂的业务规则判断和执行

## 快速开始

如果您是第一次使用逻辑编排，建议按以下步骤开始：

1. 阅读 [快速开始](./flow-start.md) 了解基本操作
2. 查看 [节点总览](./flow-node-summary.md) 熟悉各类节点功能
3. 参考 [常见问题](./flow-faq.md) 解决使用中的疑问
