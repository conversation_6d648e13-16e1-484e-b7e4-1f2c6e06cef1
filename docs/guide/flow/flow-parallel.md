# 并行节点

[[toc]]

## 概述

并行节点是一种控制结构节点，用于实现多个流程分支的**并发执行**，提高流程执行效率。

**核心特性**：
- 支持多个分支同时执行
- 各分支独立运行，互不干扰
- 适用于可并行处理的业务场景

![flow-parallel-all](/images/guide/flow/flow-parallel-all.png)

## 配置方式

### 1. 添加并行链路
- 点击 **Add** 按钮新增执行分支
- 每个分支可以独立编排业务流程

### 2. 删除并行链路  
- 点击分支对应的 **删除图标**（🗑️）移除分支
- 系统会提示确认删除操作

![flow-parallel-config](/images/guide/flow/flow-parallel-config.png)

## 运行效果

### 执行特点
- 所有分支**同时开始**执行
- 各分支执行进度独立显示
- 节点执行完成以**最后结束的分支**为准

### 效果验证
运行测试时：
1. 多个分支会**同时显示**执行状态
2. 可通过时间戳验证并发性

![flow-paralle-execute](/images/guide/flow/flow-paralle-execute.png)

## 最佳实践

1. **适用场景**：
   - 无先后依赖关系的任务
   - 耗时操作的并行处理
   - 多资源同时调用的场景

2. **注意事项**：
   - 避免分支间存在资源竞争
   - 分支数量不宜过多（建议≤5个）
