# 低代码平台交互说明

[[toc]]

## 概述
平台级别的整体交互氛围以下几个操作交互
* 工具切换区域
* 下载功能
* 换肤
* 国际化
* 推送操作

## 工具切换区域
工具平台中间的切换区域可以点击切换三个低代码工具, 平台会记住用户最近一次浏览的工具
![platform-change-tool](/images/guide/platform/platform-change-tool.jpg)

## 下载功能
**注意: 所有下载下来的页面编排、页面流编排、逻辑编排均是针对发布状态的**

下载功可以同时下载多个工具的低码产物。每一种工具下载下来是一个对应的压缩包。下载下来的文件根据放置在项目中的什么位置: 需要根据集成运形态的情况分为以下几个部分:
![platform-download](/images/guide/platform/platform-download.jpg)

xxxx TODO: 后端记得写可配置的存放路径


### 逻辑编排存放位置及配置
### 页面编排-页面流编排存放位置及配置


## 换肤、国际化
换肤、国际化没有什么特殊的, 和一些常见的系统的换肤、国际化基本一致, 直接点击操作即可

## 推送操作
推送操作是指当前的编辑态的产物推送到某个git仓库的某个路径下

xxxx TODO: 后端记得写 推送的路径配置


![platform-push](/images/guide/platform/platform-push.jpg)


