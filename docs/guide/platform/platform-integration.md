# 集成文档整体说明

[[toc]]

## 概述
整个低码平台包含三个工具, 分别是:
* 页面编排: 专注于单个页面的内部前端和业务逻辑的实现
* 页面流编排: 专注于串联多个页面的跳转关系
* 逻辑编排: 专注于整合、编排后端接口、数据结构, 串联不同接口之间的数据结构

**下面可根据自己的需求快速找到所需的集成文档(包含一份汇总的集成地址说明)**
```mermaid
flowchart LR
    A(低码需求场景)

    B(页编排场景)
    B1(目录管理集成+内页集成)

    B2(内页管理集成)

    C(页面流编排场景)
    C1(目录管理集成+内页集成)

    D(逻辑编排场景)
    D1(目录管理集成+内页集成)
    D2(内页集成)

    E(多个低码工具的平台集成)
    E1(平台集成)


    A --> B
    B e0@--> B1
    click B1 "./platform-integration.html#方式一-目录管理集成-内页集成"

    B e1@--> B2
      
    click B2 "./platform-integration.html#方式二-内页集成"


    A --> C
    C e2@--> C1
    click C1 "./platform-integration.html#目录管理集成-内页集成"


    A --> D
    D e3@--> D1
    click D1 "./platform-integration.html#方式一-目录管理集成-内页集成-1"
    D e4@--> D2
    click D2 "./platform-integration.html#方式二-内页集成-1"

    A --> E
    E e5@--> E1
    click E1 "./platform-integration.html#编辑器平台集成"

    e0@{ animation: fast }
    e1@{ animation: fast }
    e2@{ animation: fast }
    e3@{ animation: fast }
    e4@{ animation: fast }
    e5@{ animation: fast }
```

## 页面编排
### 方式一 目录管理集成+内页集成
阅读 [页面编排-页面流编排-集成文档](../page/page-integration)

::: warning 开发态、runtime集成
**前端编辑器集成地址:** /page-designer-main-entry/index.html?locale=en-US&moduleId=64#/pageManagement

| 参数 | 说明 | 是否必传 |
| --------|---------|-------|
| locale | 语言国际化(目前支持中英文 en-US、zh-CN) 默认是显示英文 | 否 |
| moduleId | 应用级别的平台集成 目前只在二次开发平台集成的时候需要传递 | 否 |

**前端通过Iframe集成的runtime地址:**  /phoenix-runtime/index.html#/page/pageName
| 参数 | 说明 | 是否必传 |
| --------|--------------------------|-------|
| pageName | 页面的名称, 需要在运行态运行的页面名称 | 是 |

:::

### 方式二 内页集成
阅读 [页面编排-页面流编排-集成文档](../page/page-integration)
::: warning 开发态、runtime集成
**前端编辑器集成地址:** /page-designer/index.html?pageId=pageId&showTabList=customized&backToPreviousText=Back to Previous#/

| 参数 | 说明 | 是否必传 |
|----------|----------------|---------|
| pageId | 页面的id | 是 |
| showTabList | 控制内页左上角的Logo是否需要返回, 不传 点击Logo无返回,传showTabList=customized,则显示返回 | 否 |
| backToPreviousText | 如果用showTabList开启了内页返回,想进一步控制返回按钮的文字,可传递参数  | 否 |

**前端通过Iframe集成的runtime地址:**  /phoenix-runtime/index.html#/page/pageName

| 参数 | 说明 | 是否必传 |
| --------|--------------------------|-------|
| pageName | 页面的名称, 需要在运行态运行的页面名称 | 是 |
:::

## 页面流编排
### 目录管理集成+内页集成
阅读 [页面编排-页面流编排-集成文档](../page/page-integration)
::: warning 开发态、runtime集成

**前端编辑器集成地址:** /pageflow-main-entry/index.html#?local=en-US&moduleId=64/journeyManagement

| 参数 | 说明 | 是否必传 |
| --------|---------|-------|
| locale | 语言国际化(目前支持中英文 en-US、zh-CN) 默认是显示英文 | 否 |
| moduleId | 应用级别的平台集成 目前只在二次开发平台集成的时候需要传递 | 否 |


**前端runtime地址:**  /phoenix-runtime/index.html#/page/pageName

| 参数 | 说明 | 是否必传 |
| --------|--------------------------|-------|
| pageName | 页面流编排中 第一个入口页面的名称 | 是 |
:::


## 逻辑编排
### 方式一 目录管理集成+内页集成
阅读 [逻辑编排集成文档](../flow/flow-integration)

::: warning 开发态、runtime集成

**前端编辑器集成地址:** /easycode/index.html?locale=en-US&moduleId=64#/flowManagement

| 参数 | 说明 | 是否必传 |
| --------|---------|-------|
| locale | 语言国际化(目前支持中英文 en-US、zh-CN) 默认是显示英文 | 否 |
| moduleId | 应用级别的平台集成 目前只在二次开发平台集成的时候需要传递 | 否 |

**对应接口的api地址:** 可通过下面两种方式查看地址
* 逻辑编排的内页界面的顶部信息图标中查看
* 在目录管理页面 => 悬浮卡片 => 点击Api detail中查看 
:::


### 方式二 内页集成
阅读 [逻辑编排集成文档](../flow/flow-integration)

::: warning 开发态、runtime集成
**前端编辑器集成地址:** /easycode/index.html?flowId=flowId&showTabList=customized&backToPreviousText=Back to Previous#/singleFlowDesign
**前端编辑器的集成地址参数说明**

| 参数 | 说明 | 是否必传 |
|----------|----------------|---------|
| flowId | 逻辑编排的id | 是 |
| showTabList | 控制内页左上角的Logo是否需要返回, 不传 点击Logo无返回,传showTabList=customized,则显示返回 | 否 |
| backToPreviousText | 如果用showTabList开启了内页返回,想进一步控制返回按钮的文字,可传递参数  | 否 |

**对应接口的api地址:** 可通过下面两种方式查看地址
* 逻辑编排的内页界面的顶部信息图标中查看
* 在目录管理页面 => 悬浮卡片 => 点击Api detail中查看 
:::


## 平台集成
### 编辑器平台集成
需要同时阅读 [页面编排-页面流编排-集成文档](../page/page-integration) 、[逻辑编排集成文档](../flow/flow-integration)

::: warning 开发态、runtime集成

**前端编辑器平台集成地址:** /easycode/index.html?moduleId=66&locale=en-US&showTabList=page,journey,flow#/pageIntegration

| 参数 | 说明 | 是否必传 |
| --------|---------|-------|
| moduleId | 应用级别的平台集成 目前只在二次开发平台集成的时候需要传递 | 否 |
| appName | 作为平台集成的时候，显示在左上角的应用名称,默认显示ZSmart Phoenix Tool | 否 | 
| locale | 语言国际化(目前支持中英文 en-US、zh-CN) 默认是显示英文 | 否 |
| showTabList | 控制平台显示哪几个工具, 默认不传,三个工具都显示,可自由组合页面编排、页面流编排、逻辑编排，<br />1 showTabList=page,flow 只显示页面编排、逻辑编排 <br /> 2 showTabList=page,journey 只显示页面编排、页面流编排 <br /> 3 showTabList=page,journey,flow 自由组合三个可选项 | 否 |

**runtime集成:** 参考各自的低码工具的runtime集成方式
:::