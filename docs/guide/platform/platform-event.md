# 低码平台事件消息交互说明

[[toc]]

# 工具侧事件消息概述

三个低码工具在不同的使用场景下会向外发出一些事件postMessage消息, 下面是一些事件消息的场景和出入参说明
::: warning **参数结构说明**
以下所有的postMessage发送出之后, 通过message监听到之后，拿到的数据结构均为以下结构

```json
{
    actionType: 'PHOENIX_XXXXXXX', // 一定存在, 并且低码平台所有的actionType结构均为PHOENIX_开头
    actionData: {
        XXXXX
    }// 看场景 有些场景有这个参数
}
```

:::

::: warning **注意**
注意事件的监听需要判断类型 避免频繁收到其他messge的干扰

```typescript
  useEffect(() => {
    const handleMessage = (event: any) => {
      // 获取消息内容
      const message = event.data;

      // **注意事件的监听需要判断类型 避免频繁收到其他messge的干扰**
      if (
        message.actionType !== 'PHOENIX_XXXX'
      ) {
        return;
      }

    };

    // 监听window message
    window.addEventListener('message', handleMessage);

    // 销毁window message
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);
```

:::

## 页面编排

| 事件消息code(指的是actionType字段) | 参数说明                                                                                                                                                                                                                                                                      | 触发场景                                                              |
| ---------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------- |
| PHOENIX_PAGE_GO_TO_PREVIOUS        | 点击内页的返回向外发送消息                                                                                                                                                                                                                                                    | 以方式二 单独嵌内页时 且传递了showTabList=customized 开启了自定义返回 |
| PHOENIX_PAGE_SAVE                  | actionData里面会有当前页面的版本状态信息<br /> ``json { triggerType: 'PAGE_SAVE' \| 'COMPONENT_SAVE' \| 'TEMPLATE_SAVE', // page、component、template的保存均会触发，用这个字段作区分 versionState: '版本状态', pageData: {XXX} // pageData里面包含了当前保存的页面的信息  } `` | 页面点击保存按钮的时候                                                |

## 页面流编排

| 事件消息code(指的是actionType字段) | 参数说明                                                                               | 触发场景                                                              |
| ---------------------------------- | -------------------------------------------------------------------------------------- | --------------------------------------------------------------------- |
| PHOENIX_JOURNEY_GO_TO_PREVIOUS     | 点击内页的返回向外发送消息                                                             | 以方式二 单独嵌内页时 且传递了showTabList=customized 开启了自定义返回 |
| PHOENIX_JOURNEY_SAVE               | actionData里面会有当前页面的版本状态信息<br /> ``json {  versionState: '版本状态' } `` | 页面点击保存按钮的时候                                                |

## 逻辑编排

| 事件消息code(指的是actionType字段) | 参数说明                                                                               | 触发场景                                                              |
| ---------------------------------- | -------------------------------------------------------------------------------------- | --------------------------------------------------------------------- |
| PHOENIX_FLOW_GO_TO_PREVIOUS        | 点击内页的返回向外发送消息                                                             | 以方式二 单独嵌内页时 且传递了showTabList=customized 开启了自定义返回 |
| PHOENIX_FLOW_SAVE                  | actionData里面会有当前页面的版本状态信息<br /> ``json {  versionState: '版本状态' } `` | 页面点击保存按钮的时候                                                |

# 业务集成消息说明概述

  页面编排的界面、页面流串联出来的页面可能会需要和外部的集成方进行交互, 比如集成方的某些交互需要触发低代码页面的交互、或者低代码界面的某些交需要触发集成方的界面中的某些交互,
  这个时候就需要使用下面的通信方式来达到这样的目的

  整体的通信交互方式使用的是postMessage通信。

## 页面编排、页面流编排

  ::: warning **参数结构说明**
  page和集成方的交互均遵循以下的数据结构, 即 发送数据和接收数据均是这样的结构

```json
  {
      eventName: 'XXXXXX', // 此事件的事件名称 需要在低码页面里面定义好
      data: {
          XXXXX
      }// 此事件参数的数据结构 需要在低码页面里面定义结构
  }
```

  :::

  下面以两个具体场景解释如何操作:

* 集成方发出消息 低码页面接收消息做出动作
* 低码界面发出消息 集成方接收并拿到通信数据

### 集成方发出消息 低码页面接收消息做出动作

  举个例子: 集成方发出消息, 触发低码表单的联动修改

  1、低码界面内定义事件名称以及交互的参数结构
  ![page-outer-trigger-inter-1](/images/guide/page/page-outer-trigger-inter-1.jpg)

  2、低码界面配接收参数, 并将接收到的参数赋值到表单上
  ![page-outer-trigger-inter-](/images/guide/page/page-outer-trigger-inter-2.jpg)

  ![page-outer-trigger-inter-3](/images/guide/page/page-outer-trigger-inter-3.jpg)

  ![page-outer-trigger-inter-4](/images/guide/page/page-outer-trigger-inter-4.jpg)

  3、集成方发送消息
  ::: warning 集成方发送消息 发送完上面的表单会自动填充上发送的两个字段的值

```javascript
    window.postMessage(
      {
        eventName: 'a4YOJhgU_outTriggerInterPage', // eventName是在低码中定义的事件名称 注意地址事件名称的时候 别忘记了复制前缀名称
        data: { // data的结构需要和在低码页面对应的事件中的定义的参数名称一致 否则会出现在低码界面里面找不到的情况
          name: 'zhangsan',
          age: 88
        }
      },
      '*',
    );
```

  :::

  4、最终效果如下

  ![page-outer-trigger-inter-](/images/guide/page/page-outer-trigger-inter-5.jpg)

### 低码界面发出消息 集成方接收并拿到通信数据

  举个例子 低码界面的某个表单的某个字段值修改 集成方需要实时拿到这个变化后的值

  1、低码界面内定义事件名称以及交互的参数结构
  ![page-outer-trigger-inter-1](/images/guide/page/page-outer-trigger-inter-1.jpg)

  2、低码界面配接发送动作和发送数据, 比如表单的name修改, 需要告知集成方name的实时值
  ![page-explain-4](/images/guide/page/page-explain-4.jpg)

  3、集成方接收消息
  ::: warning 集成方接收消息 下面的代码是以react为例

```typescript
    useEffect(() => {
    const handleMessage = (event: any) => {
      // 获取消息内容
      const message = event.data;

      // **注意事件的监听需要判断事件名称 避免频繁收到其他messge的干扰**
      if (
        message.eventName !== 'a4YOJhgU_outTriggerInterPage' // 注意eventName在低码配置界面里面的事件名称的前缀也是事件名称的一部分
      ) {
        return;
      }
    };

    console.log('接收数据', event.data); 
    // 接收结构如下
    // {
    //   eveneName: 'a4YOJhgU_outTriggerInterPage',
    //   data: {
    //     name: 'xxxx'
    //   }
    // }

    // 监听window message
    window.addEventListener('message', handleMessage);

    // 销毁window message
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);
```

  :::
