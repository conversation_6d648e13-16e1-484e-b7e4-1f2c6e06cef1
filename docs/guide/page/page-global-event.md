# 全局广播事件

[[toc]]

# 概述
全局广播事件是一种比较灵活的通信方式,主要在以下两个方面有应用场景.本章节主要着重说明应用场景1的使用.
* 应用场景1 页面内部交互联动,可以不用刻意去在乎组件的层级结构而实现联动
* 应用场景2 低码界面和外部集成的交互联动 [业务集成消息说明](/guide/platform/platform-event.html#业务集成消息说明概述)

# 全局广播事件配置
1、全局广播事件的配置入口、交互说明
![page-explain-1](/images/guide/page/page-explain-1.jpg)

![page-explain-2](/images/guide/page/page-explain-2.jpg)


### 全局广播事件的发送
**在某个需要的时机,比如按钮点击、文本框修改, 配置触发广播事件的发送动作**

在发送动作上,可以发送具体的业务数据(业务数据的结构是在上面的广播配置中定义的), 

eg.表单项的name改变 触发全局广播事件
![page-explain-3](/images/guide/page/page-explain-3.jpg)

![page-explain-4](/images/guide/page/page-explain-4.jpg)

   
### 全局广播事件的接收
**在需要的组件上绑定全局广播事件的监听动作, 再在监听动作下配置具体的事件响应动作**

在接收动作上可以拿到广播事件传递过来值(值的结构是在上面的广播配置中定义的)

eg.监听上述的全局广播事件, 并给表单赋值
![page-explain-5](/images/guide/page/page-explain-5.jpg)

![page-explain-6](/images/guide/page/page-explain-6.jpg)

整体效果如下: 这样便实现了一个联动
![page-explain-7](/images/guide/page/page-explain-7.jpg)



