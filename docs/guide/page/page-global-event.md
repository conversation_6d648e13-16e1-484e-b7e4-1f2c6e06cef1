# 全局广播事件

[[toc]]

## 概述

全局广播事件是一种灵活的跨组件通信机制，主要应用于以下场景：

1. **页面内部交互联动**
   - 无需关注组件层级结构
   - 实现任意组件间的通信
2. **低码界面与外部集成联动**  
   - [业务集成消息说明](/guide/platform/platform-event.html#业务集成消息说明概述)

*注：本章节重点说明应用场景1的使用*

## 配置说明

### 1. 基本配置
- **配置入口**：通过事件管理中心进行广播事件定义
- **交互流程**：
  1. 定义事件名称
  2. 指定事件数据结构
  3. 配置触发条件

![page-explain-1](/images/guide/page/page-explain-1.jpg)
![page-explain-2](/images/guide/page/page-explain-2.jpg)

## 使用指南

### 2. 事件发送
**触发时机**：
- 按钮点击
- 表单项修改
- 其他交互事件

**配置要点**：
- 绑定发送动作到触发事件
- 设置要传递的业务数据（需符合预定义结构）

*示例：表单项name改变触发广播事件*
![page-explain-3](/images/guide/page/page-explain-3.jpg)
![page-explain-4](/images/guide/page/page-explain-4.jpg)

### 3. 事件接收
**实现步骤**：
1. 在目标组件绑定事件监听
2. 配置事件响应动作
3. 处理接收到的数据

**数据获取**：
- 可直接使用广播传递的值
- 数据结构与发送端一致

*示例：监听广播事件并更新表单*
![page-explain-5](/images/guide/page/page-explain-5.jpg)
![page-explain-6](/images/guide/page/page-explain-6.jpg)

## 效果演示

实现组件间联动效果：
![page-explain-7](/images/guide/page/page-explain-7.jpg)

## 最佳实践
1. 数据结构保持简洁
2. 避免循环触发
3. 建议添加事件说明注释







