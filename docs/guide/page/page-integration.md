# 页面编排-页面流编排集成文档

[[toc]]

## 概述
页面编排和页面流编排的关系是紧密相连，它们总是"成对"出现,所以他们的集成是类似的, 集成一个或者两个都需要集成这个架包。 

## 集成方式
### 页面编排

#### 编辑态
###### &nbsp;&nbsp;方式一 目录管理集成+内页集成
::: warning 开发态
**前端编辑器集成地址:** /page-designer-main-entry/index.html?locale=en-US&moduleId=64#/pageManagement

| 参数 | 说明 | 是否必传 |
| --------|---------|-------|
| locale | 语言国际化(目前支持中英文 en-US、zh-CN) 默认是显示英文 | 否 |
| moduleId | 应用级别的平台集成 目前只在二次开发平台集成的时候需要传递 | 否 |

:::

###### &nbsp;&nbsp;方式二 内页集成
::: warning 开发态
**前端编辑器集成地址:** /page-designer/index.html?pageId=pageId&showTabList=customized&backToPreviousText=Back to Previous#/

| 参数 | 说明 | 是否必传 |
|----------|----------------|---------|
| pageId | 页面的id | 是 |
| showTabList | 控制内页左上角的Logo是否需要返回, 不传 点击Logo无返回,传showTabList=customized,则显示返回 | 否 |
| backToPreviousText | 如果用showTabList开启了内页返回,想进一步控制返回按钮的文字,可传递参数  | 否 |

:::

#### 运行态

###### &nbsp;&nbsp;方式一 目录管理集成+内页集成
::: warning runtime集成
**前端通过Iframe集成的runtime地址:**  /phoenix-runtime/index.html#/page/pageName
| 参数 | 说明 | 是否必传 |
| --------|--------------------------|-------|
| pageName | 页面的名称, 需要在运行态运行的页面名称 | 是 |

:::

###### &nbsp;&nbsp;方式二 内页集成
::: warning runtime集成
**前端通过Iframe集成的runtime地址:**  /phoenix-runtime/index.html#/page/pageName

| 参数 | 说明 | 是否必传 |
| --------|--------------------------|-------|
| pageName | 页面的名称, 需要在运行态运行的页面名称 | 是 |
:::

##### 后端集成



### 页面流编排
#### 编辑态

::: warning 开发态

**前端编辑器集成地址:** /pageflow-main-entry/index.html#?local=en-US&moduleId=64/journeyManagement

| 参数 | 说明 | 是否必传 |
| --------|---------|-------|
| locale | 语言国际化(目前支持中英文 en-US、zh-CN) 默认是显示英文 | 否 |
| moduleId | 应用级别的平台集成 目前只在二次开发平台集成的时候需要传递 | 否 |

:::

#### 运行态
::: warning runtime集成

**前端runtime地址:**  /phoenix-runtime/index.html#/page/pageName

| 参数 | 说明 | 是否必传 |
| --------|--------------------------|-------|
| pageName | 页面流编排中 第一个入口页面的名称 | 是 |
:::
